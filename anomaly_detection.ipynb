{"cells": [{"cell_type": "code", "execution_count": null, "id": "f4739609", "metadata": {}, "outputs": [], "source": ["from typing import Dict\n", "import numpy as np\n", "import sy\n", "sys.path.append(\"./EasyTSAD-bench\")\n", "from EasyTSAD.Controller import TSADController\n", "from opto.trace import node, bundle"]}, {"cell_type": "code", "execution_count": 2, "id": "7014eb4d", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'\n", "os.environ['TRACE_CUSTOMLLM_URL'] = \"http://21.91.109.192:30000/v1\"\n", "os.environ['TRACE_CUSTOMLLM_MODEL'] = \"deepseek-ai/DeepSeek-V3\"\n", "os.environ['TRACE_CUSTOMLLM_API_KEY'] = \"sk-dum7ge6k3FR8ThN7xZmnaWxjv1PPv9OsO7JMjbOu5tVqxDEt\""]}, {"cell_type": "code", "execution_count": 3, "id": "2d8ba58b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-13 15:55:37,058) [INFO]: \n", "                         \n", "███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ \n", "██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗\n", "█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║\n", "██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║\n", "███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝\n", "╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ \n", "                                                                      \n", "                         \n", "(2025-08-13 15:55:37,059) [INFO]: Dataset Directory has been loaded.\n"]}], "source": ["gctrl = TSADController()\n", "\n", "gctrl.set_dataset(\n", "    dataset_type=\"UTS\",\n", "    dirname=\"./datasets\",\n", "    datasets=[\"AIOPS\", \"WSD\"],\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "0334104d", "metadata": {}, "outputs": [], "source": ["from anomaly_detection_system import OnlineAnomalyDetector"]}, {"cell_type": "code", "execution_count": 5, "id": "e31f1b67", "metadata": {}, "outputs": [], "source": ["from EasyTSAD.Methods import BaseMethod\n", "from EasyTSAD.DataFactory import TSData\n", "\n", "class AnomalyDetector(BaseMethod):\n", "    def __init__(self, hparams) -> None:\n", "        super().__init__()\n", "        self.__anomaly_score = None\n", "        self.name = \"AnomalyDetector\"\n", "    \n", "    def train_valid_phase(self, tsTrain: TSData):\n", "        '''\n", "        Define train and valid phase for naive mode. All time series needed are saved in tsTrain. \n", "        \n", "        tsTrain's property :\n", "            train (np.ndarray):\n", "                The training set in numpy format;\n", "            valid (np.ndarray):\n", "                The validation set in numpy format;\n", "            test (np.ndarray):\n", "                The test set in numpy format;\n", "            train_label (np.ndarray):\n", "                The labels of training set in numpy format;\n", "            test_label (np.ndarray):\n", "                The labels of test set in numpy format;\n", "            valid_label (np.ndarray):\n", "                The labels of validation set in numpy format;\n", "            info (dict):\n", "                Some informations about the dataset, which might be useful.\n", "                \n", "        NOTE : test and test_label are not accessible in training phase\n", "        '''\n", "        ...\n", "        \n", "        return\n", "            \n", "    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):\n", "        '''\n", "        Define train and valid phase for all-in-one mode. All time series needed are saved in tsTrains. \n", "        \n", "        tsTrain's structure:\n", "            {\n", "                \"name of time series 1\": tsData1,\n", "                \"name of time series 2\": tsData2,\n", "                ...\n", "            }\n", "            \n", "        '''\n", "        ...\n", "        \n", "        return\n", "        \n", "    #@bundle(trainable=True)\n", "    def test_phase(self, tsData: TSData):\n", "        '''\n", "        Given a time series splitted into train/valid/test parts, generate anomaly scores for the test part.\n", "        \n", "        Args:\n", "            tsData (TSData): The time series data.\n", "            tsData's property that can be used:\n", "                train (np.ndarray):\n", "                    The training set in numpy format;\n", "                valid (np.ndarray):\n", "                    The validation set in numpy format;\n", "                test (np.ndarray):\n", "                    The test set in numpy format;\n", "        \n", "        return:\n", "            None. You should save the anomaly scores in self.__anomaly_score.\n", "            For each time series, the length of anomaly scores should be equal to the length of test set.   \n", "            For a location that is likely to be an anomaly, the corressponding anomaly score should be larger than others.\n", "        '''\n", "        \n", "        # For example\n", "        test_len = tsData.test.shape[0]\n", "        cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])\n", "        score = cat_data[1:] - cat_data[:-1]\n", "        anomaly_score = score[-test_len:]\n", "        self.__anomaly_score = anomaly_score\n", "\n", "        #anomaly_score = []\n", "        #detector = OnlineAnomalyDetector()\n", "        #for obs in tsData.test:\n", "        #    score, flag = detector.process_point(obs)\n", "        #    anomaly_score.append(score)\n", "        #self.__anomaly_score = np.array(anomaly_score)\n", "        #print(self.__anomaly_score)\n", "            \n", "        \n", "    def anomaly_score(self) -> np.ndarray:\n", "        return self.__anomaly_score\n", "    \n", "    def param_statistic(self, save_file):\n", "        param_info = \"Your Algo. info\"\n", "        with open(save_file, 'w') as f:\n", "            f.write(param_info)"]}, {"cell_type": "code", "execution_count": 6, "id": "6dda675f", "metadata": {}, "outputs": [], "source": ["from EasyTSAD.Methods import BaseMethod\n", "from EasyTSAD.DataFactory import TSData\n", "\n", "class YetAnotherAnomalyDetector(BaseMethod):\n", "    def __init__(self, hparams) -> None:\n", "        super().__init__()\n", "        self.__anomaly_score = None\n", "        self.name = \"YetAnotherAnomalyDetector\"\n", "    \n", "    def train_valid_phase(self, tsTrain: TSData):\n", "        '''\n", "        Define train and valid phase for naive mode. All time series needed are saved in tsTrain. \n", "        \n", "        tsTrain's property :\n", "            train (np.ndarray):\n", "                The training set in numpy format;\n", "            valid (np.ndarray):\n", "                The validation set in numpy format;\n", "            test (np.ndarray):\n", "                The test set in numpy format;\n", "            train_label (np.ndarray):\n", "                The labels of training set in numpy format;\n", "            test_label (np.ndarray):\n", "                The labels of test set in numpy format;\n", "            valid_label (np.ndarray):\n", "                The labels of validation set in numpy format;\n", "            info (dict):\n", "                Some informations about the dataset, which might be useful.\n", "                \n", "        NOTE : test and test_label are not accessible in training phase\n", "        '''\n", "        ...\n", "        \n", "        return\n", "            \n", "    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):\n", "        '''\n", "        Define train and valid phase for all-in-one mode. All time series needed are saved in tsTrains. \n", "        \n", "        tsTrain's structure:\n", "            {\n", "                \"name of time series 1\": tsData1,\n", "                \"name of time series 2\": tsData2,\n", "                ...\n", "            }\n", "            \n", "        '''\n", "        ...\n", "        \n", "        return\n", "        \n", "    #@bundle(trainable=True)\n", "    def test_phase(self, tsData: TSData):\n", "        '''\n", "        Given a time series splitted into train/valid/test parts, generate anomaly scores for the test part.\n", "        \n", "        Args:\n", "            tsData (TSData): The time series data.\n", "            tsData's property that can be used:\n", "                train (np.ndarray):\n", "                    The training set in numpy format;\n", "                valid (np.ndarray):\n", "                    The validation set in numpy format;\n", "                test (np.ndarray):\n", "                    The test set in numpy format;\n", "        \n", "        return:\n", "            None. You should save the anomaly scores in self.__anomaly_score.\n", "            For each time series, the length of anomaly scores should be equal to the length of test set.   \n", "            For a location that is likely to be an anomaly, the corressponding anomaly score should be larger than others.\n", "        '''\n", "        \n", "        # For example\n", "        # test_len = tsData.test.shape[0]\n", "        # cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])\n", "        # score = cat_data[1:] - cat_data[:-1]\n", "        # anomaly_score = score[-test_len:]\n", "        # self.__anomaly_score = anomaly_score\n", "\n", "        anomaly_score = []\n", "        detector = OnlineAnomalyDetector()\n", "        for obs in tsData.test:\n", "            score, flag = detector.process_point(obs)\n", "            anomaly_score.append(score)\n", "        self.__anomaly_score = np.array(anomaly_score)\n", "            \n", "        \n", "    def anomaly_score(self) -> np.ndarray:\n", "        return self.__anomaly_score\n", "    \n", "    def param_statistic(self, save_file):\n", "        param_info = \"Your Algo. info\"\n", "        with open(save_file, 'w') as f:\n", "            f.write(param_info)"]}, {"cell_type": "code", "execution_count": 7, "id": "cad29321", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-13 15:55:37,079) [INFO]: Run Experiments. Method[YetAnotherAnomalyDetector], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-13 15:55:37,079) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml\n", "(2025-08-13 15:55:37,080) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD \n", "(2025-08-13 15:55:37,167) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 \n", "(2025-08-13 15:56:00,835) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 \n", "(2025-08-13 15:56:24,951) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 \n", "(2025-08-13 15:56:46,012) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 \n", "(2025-08-13 15:57:10,240) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 \n", "(2025-08-13 15:57:34,144) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c \n", "(2025-08-13 15:57:51,713) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 \n", "(2025-08-13 15:58:09,372) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 \n", "(2025-08-13 15:58:30,449) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa \n", "(2025-08-13 15:58:54,597) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 \n", "(2025-08-13 15:58:55,270) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 \n", "(2025-08-13 15:59:16,810) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd \n", "(2025-08-13 15:59:41,019) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd \n", "(2025-08-13 15:59:41,686) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 \n", "(2025-08-13 15:59:59,333) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd \n", "(2025-08-13 16:00:16,524) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 \n", "(2025-08-13 16:00:40,636) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 \n", "(2025-08-13 16:01:04,701) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d \n", "(2025-08-13 16:01:05,353) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 \n", "(2025-08-13 16:01:06,609) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da \n", "(2025-08-13 16:01:27,744) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea \n", "(2025-08-13 16:01:28,408) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa \n", "(2025-08-13 16:01:49,475) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 \n", "(2025-08-13 16:01:50,115) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 \n", "(2025-08-13 16:01:50,759) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 \n", "(2025-08-13 16:02:15,057) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d \n", "(2025-08-13 16:02:39,197) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 \n", "(2025-08-13 16:03:00,478) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af \n", "(2025-08-13 16:03:24,746) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 \n", "(2025-08-13 16:03:45,999) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 135 \n", "(2025-08-13 16:03:47,767) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 61 \n", "(2025-08-13 16:03:49,990) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 95 \n", "(2025-08-13 16:03:52,229) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 132 \n", "(2025-08-13 16:03:54,454) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 59 \n", "(2025-08-13 16:03:56,598) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 92 \n", "(2025-08-13 16:03:58,846) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 66 \n", "(2025-08-13 16:04:01,099) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 104 \n", "(2025-08-13 16:04:03,336) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 50 \n", "(2025-08-13 16:04:05,552) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 68 \n", "(2025-08-13 16:04:07,785) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 103 \n", "(2025-08-13 16:04:10,056) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 57 \n", "(2025-08-13 16:04:12,247) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 168 \n", "(2025-08-13 16:04:14,426) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 157 \n", "(2025-08-13 16:04:16,649) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 150 \n", "(2025-08-13 16:04:18,895) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 159 \n", "(2025-08-13 16:04:21,162) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 32 \n", "(2025-08-13 16:04:23,366) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 166 \n", "(2025-08-13 16:04:25,580) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 192 \n", "(2025-08-13 16:04:27,788) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 35 \n", "(2025-08-13 16:04:30,019) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 195 \n", "(2025-08-13 16:04:32,234) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 161 \n", "(2025-08-13 16:04:34,429) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 102 \n", "(2025-08-13 16:04:36,602) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 69 \n", "(2025-08-13 16:04:38,813) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 56 \n", "(2025-08-13 16:04:41,062) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 105 \n", "(2025-08-13 16:04:43,318) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 51 \n", "(2025-08-13 16:04:45,522) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 58 \n", "(2025-08-13 16:04:47,864) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 133 \n", "(2025-08-13 16:04:50,182) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 67 \n", "(2025-08-13 16:04:52,398) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 93 \n", "(2025-08-13 16:04:54,623) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 134 \n", "(2025-08-13 16:04:56,777) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 94 \n", "(2025-08-13 16:04:59,040) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 60 \n", "(2025-08-13 16:05:01,287) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 34 \n", "(2025-08-13 16:05:03,543) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 160 \n", "(2025-08-13 16:05:05,774) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 194 \n", "(2025-08-13 16:05:08,005) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 33 \n", "(2025-08-13 16:05:10,263) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 158 \n", "(2025-08-13 16:05:12,520) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 193 \n", "(2025-08-13 16:05:14,762) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 167 \n", "(2025-08-13 16:05:16,974) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 151 \n", "(2025-08-13 16:05:19,187) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 169 \n", "(2025-08-13 16:05:21,397) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 156 \n", "(2025-08-13 16:05:23,601) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 204 \n", "(2025-08-13 16:05:25,841) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 203 \n", "(2025-08-13 16:05:28,080) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 202 \n", "(2025-08-13 16:05:30,303) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 205 \n", "(2025-08-13 16:05:32,525) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 174 \n", "(2025-08-13 16:05:34,743) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 180 \n", "(2025-08-13 16:05:36,964) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 20 \n", "(2025-08-13 16:05:39,211) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 187 \n", "(2025-08-13 16:05:41,444) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 18 \n", "(2025-08-13 16:05:43,655) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 173 \n", "(2025-08-13 16:05:45,855) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 27 \n", "(2025-08-13 16:05:48,063) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 9 \n", "(2025-08-13 16:05:50,273) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 0 \n", "(2025-08-13 16:05:52,493) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 145 \n", "(2025-08-13 16:05:54,820) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 11 \n", "(2025-08-13 16:05:57,013) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 142 \n", "(2025-08-13 16:05:59,232) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 7 \n", "(2025-08-13 16:06:01,451) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 29 \n", "(2025-08-13 16:06:03,660) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 16 \n", "(2025-08-13 16:06:05,864) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 189 \n", "(2025-08-13 16:06:08,091) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 129 \n", "(2025-08-13 16:06:10,309) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 42 \n", "(2025-08-13 16:06:12,549) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 89 \n", "(2025-08-13 16:06:14,766) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 116 \n", "(2025-08-13 16:06:16,997) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 45 \n", "(2025-08-13 16:06:19,177) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 111 \n", "(2025-08-13 16:06:21,426) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 73 \n", "(2025-08-13 16:06:23,669) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 118 \n", "(2025-08-13 16:06:25,879) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 87 \n", "(2025-08-13 16:06:28,079) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 127 \n", "(2025-08-13 16:06:30,288) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 80 \n", "(2025-08-13 16:06:32,524) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 74 \n", "(2025-08-13 16:06:34,758) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 120 \n", "(2025-08-13 16:06:36,960) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 6 \n", "(2025-08-13 16:06:39,168) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 28 \n", "(2025-08-13 16:06:41,374) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 143 \n", "(2025-08-13 16:06:43,586) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 17 \n", "(2025-08-13 16:06:45,780) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 188 \n", "(2025-08-13 16:06:47,991) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 144 \n", "(2025-08-13 16:06:50,142) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 1 \n", "(2025-08-13 16:06:52,350) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 10 \n", "(2025-08-13 16:06:54,694) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 172 \n", "(2025-08-13 16:06:56,942) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 186 \n", "(2025-08-13 16:06:59,184) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 19 \n", "(2025-08-13 16:07:01,418) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 26 \n", "(2025-08-13 16:07:03,633) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 8 \n", "(2025-08-13 16:07:05,850) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 181 \n", "(2025-08-13 16:07:08,075) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 175 \n", "(2025-08-13 16:07:10,318) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 21 \n", "(2025-08-13 16:07:12,530) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 75 \n", "(2025-08-13 16:07:14,709) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 81 \n", "(2025-08-13 16:07:16,961) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 121 \n", "(2025-08-13 16:07:19,175) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 119 \n", "(2025-08-13 16:07:21,389) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 86 \n", "(2025-08-13 16:07:23,606) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 72 \n", "(2025-08-13 16:07:25,815) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 126 \n", "(2025-08-13 16:07:28,040) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 44 \n", "(2025-08-13 16:07:30,271) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 110 \n", "(2025-08-13 16:07:32,515) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 43 \n", "(2025-08-13 16:07:34,715) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 128 \n", "(2025-08-13 16:07:36,909) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 88 \n", "(2025-08-13 16:07:39,130) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 117 \n", "(2025-08-13 16:07:41,362) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 198 \n", "(2025-08-13 16:07:43,588) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 153 \n", "(2025-08-13 16:07:45,820) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 38 \n", "(2025-08-13 16:07:48,021) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 154 \n", "(2025-08-13 16:07:50,232) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 36 \n", "(2025-08-13 16:07:52,459) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 196 \n", "(2025-08-13 16:07:54,646) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 162 \n", "(2025-08-13 16:07:56,860) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 31 \n", "(2025-08-13 16:07:59,077) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 165 \n", "(2025-08-13 16:08:01,277) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 191 \n", "(2025-08-13 16:08:03,495) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 131 \n", "(2025-08-13 16:08:05,706) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 91 \n", "(2025-08-13 16:08:07,917) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 65 \n", "(2025-08-13 16:08:10,139) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 136 \n", "(2025-08-13 16:08:12,897) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 62 \n", "(2025-08-13 16:08:15,097) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 96 \n", "(2025-08-13 16:08:17,296) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 109 \n", "(2025-08-13 16:08:19,496) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 100 \n", "(2025-08-13 16:08:21,717) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 54 \n", "(2025-08-13 16:08:23,927) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 107 \n", "(2025-08-13 16:08:26,132) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 98 \n", "(2025-08-13 16:08:28,323) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 138 \n", "(2025-08-13 16:08:30,538) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 53 \n", "(2025-08-13 16:08:32,684) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 30 \n", "(2025-08-13 16:08:34,892) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 190 \n", "(2025-08-13 16:08:37,102) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 164 \n", "(2025-08-13 16:08:39,307) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 37 \n", "(2025-08-13 16:08:41,495) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 163 \n", "(2025-08-13 16:08:43,687) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 197 \n", "(2025-08-13 16:08:45,892) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 155 \n", "(2025-08-13 16:08:48,071) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 199 \n", "(2025-08-13 16:08:50,280) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 39 \n", "(2025-08-13 16:08:52,479) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 152 \n", "(2025-08-13 16:08:54,709) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 106 \n", "(2025-08-13 16:08:56,906) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 99 \n", "(2025-08-13 16:08:59,123) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 52 \n", "(2025-08-13 16:09:01,444) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 139 \n", "(2025-08-13 16:09:03,647) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 101 \n", "(2025-08-13 16:09:05,858) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 55 \n", "(2025-08-13 16:09:08,071) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 137 \n", "(2025-08-13 16:09:10,172) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 97 \n", "(2025-08-13 16:09:12,382) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 108 \n", "(2025-08-13 16:09:14,549) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 63 \n", "(2025-08-13 16:09:16,735) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 130 \n", "(2025-08-13 16:09:18,942) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 64 \n", "(2025-08-13 16:09:21,135) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 90 \n", "(2025-08-13 16:09:23,329) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 209 \n", "(2025-08-13 16:09:25,526) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 200 \n", "(2025-08-13 16:09:27,718) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 207 \n", "(2025-08-13 16:09:29,935) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 206 \n", "(2025-08-13 16:09:32,136) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 201 \n", "(2025-08-13 16:09:34,340) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 208 \n", "(2025-08-13 16:09:36,552) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 46 \n", "(2025-08-13 16:09:38,860) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 79 \n", "(2025-08-13 16:09:41,078) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 112 \n", "(2025-08-13 16:09:43,289) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 41 \n", "(2025-08-13 16:09:45,498) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 115 \n", "(2025-08-13 16:09:47,718) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 83 \n", "(2025-08-13 16:09:49,920) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 77 \n", "(2025-08-13 16:09:52,159) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 123 \n", "(2025-08-13 16:09:54,347) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 48 \n", "(2025-08-13 16:09:56,552) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 70 \n", "(2025-08-13 16:09:58,750) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 84 \n", "(2025-08-13 16:10:00,954) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 124 \n", "(2025-08-13 16:10:03,172) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 184 \n", "(2025-08-13 16:10:05,358) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 170 \n", "(2025-08-13 16:10:07,580) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 24 \n", "(2025-08-13 16:10:09,793) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 177 \n", "(2025-08-13 16:10:11,988) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 183 \n", "(2025-08-13 16:10:14,205) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 148 \n", "(2025-08-13 16:10:16,873) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 23 \n", "(2025-08-13 16:10:19,078) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 141 \n", "(2025-08-13 16:10:21,280) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 4 \n", "(2025-08-13 16:10:23,490) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 15 \n", "(2025-08-13 16:10:25,698) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 3 \n", "(2025-08-13 16:10:27,900) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 146 \n", "(2025-08-13 16:10:30,024) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 12 \n", "(2025-08-13 16:10:32,204) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 179 \n", "(2025-08-13 16:10:34,399) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 85 \n", "(2025-08-13 16:10:36,619) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 71 \n", "(2025-08-13 16:10:38,827) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 125 \n", "(2025-08-13 16:10:41,035) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 76 \n", "(2025-08-13 16:10:43,229) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 82 \n", "(2025-08-13 16:10:45,434) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 49 \n", "(2025-08-13 16:10:47,640) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 122 \n", "(2025-08-13 16:10:49,841) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 40 \n", "(2025-08-13 16:10:52,058) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 114 \n", "(2025-08-13 16:10:54,278) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 47 \n", "(2025-08-13 16:10:56,409) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 113 \n", "(2025-08-13 16:10:58,625) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 78 \n", "(2025-08-13 16:11:00,827) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 147 \n", "(2025-08-13 16:11:02,581) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 2 \n", "(2025-08-13 16:11:04,826) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 178 \n", "(2025-08-13 16:11:07,035) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 13 \n", "(2025-08-13 16:11:09,248) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 5 \n", "(2025-08-13 16:11:11,454) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 140 \n", "(2025-08-13 16:11:13,657) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 14 \n", "(2025-08-13 16:11:15,879) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 182 \n", "(2025-08-13 16:11:18,092) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 176 \n", "(2025-08-13 16:11:20,297) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 22 \n", "(2025-08-13 16:11:22,491) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 149 \n", "(2025-08-13 16:11:24,703) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 171 \n", "(2025-08-13 16:11:26,938) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 185 \n", "(2025-08-13 16:11:29,130) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 25 \n", "(2025-08-13 16:11:31,714) [INFO]: Register evaluations\n", "(2025-08-13 16:11:31,714) [INFO]: Perform evaluations. Method[YetAnotherAnomalyDetector], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-13 16:11:31,714) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD \n", "(2025-08-13 16:11:32,101) [INFO]:     [YetAnotherAnomalyDetector] Eval dataset AIOPS <<<\n", "(2025-08-13 16:11:32,102) [INFO]:         [AIOPS] Using margins (0, 5)\n", "(2025-08-13 16:11:38,583) [INFO]:     [YetAnotherAnomalyDetector] Eval dataset WSD <<<\n", "(2025-08-13 16:11:38,583) [INFO]:         [WSD] Using margins (0, 3)\n", "(2025-08-13 16:11:38,690) [WARNING]: [YetAnotherAnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,251) [WARNING]: [YetAnotherAnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,396) [WARNING]: [YetAnotherAnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,507) [WARNING]: [YetAnotherAnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,621) [WARNING]: [YetAnotherAnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,656) [WARNING]: [YetAnotherAnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,767) [WARNING]: [YetAnotherAnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,951) [WARNING]: [YetAnotherAnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:39,951) [WARNING]: [YetAnotherAnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,061) [WARNING]: [YetAnotherAnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,062) [WARNING]: [YetAnotherAnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,175) [WARNING]: [YetAnotherAnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,324) [WARNING]: [YetAnotherAnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,583) [WARNING]: [YetAnotherAnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,584) [WARNING]: [YetAnotherAnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,585) [WARNING]: [YetAnotherAnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,586) [WARNING]: [YetAnotherAnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,586) [WARNING]: [YetAnotherAnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,702) [WARNING]: [YetAnotherAnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,851) [WARNING]: [YetAnotherAnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:40,999) [WARNING]: [YetAnotherAnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,000) [WARNING]: [YetAnotherAnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,268) [WARNING]: [YetAnotherAnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,269) [WARNING]: [YetAnotherAnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,380) [WARNING]: [YetAnotherAnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,497) [WARNING]: [YetAnotherAnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,604) [WARNING]: [YetAnotherAnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,605) [WARNING]: [YetAnotherAnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,794) [WARNING]: [YetAnotherAnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:41,905) [WARNING]: [YetAnotherAnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,175) [WARNING]: [YetAnotherAnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,176) [WARNING]: [YetAnotherAnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,177) [WARNING]: [YetAnotherAnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,178) [WARNING]: [YetAnotherAnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,218) [WARNING]: [YetAnotherAnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,219) [WARNING]: [YetAnotherAnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,451) [WARNING]: [YetAnotherAnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,673) [WARNING]: [YetAnotherAnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:42,909) [WARNING]: [YetAnotherAnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:43,178) [WARNING]: [YetAnotherAnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:43,490) [WARNING]: [YetAnotherAnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:43,766) [WARNING]: [YetAnotherAnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:43,767) [WARNING]: [YetAnotherAnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:43,955) [WARNING]: [YetAnotherAnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:43,956) [WARNING]: [YetAnotherAnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:44,458) [WARNING]: [YetAnotherAnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:44,615) [WARNING]: [YetAnotherAnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:44,616) [WARNING]: [YetAnotherAnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:44,695) [WARNING]: [YetAnotherAnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-13 16:11:44,709) [INFO]: Plotting. Method[YetAnotherAnomalyDetector], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-13 16:11:44,709) [INFO]:     [Load Data (All)] DataSets: AIOP<PERSON>,WSD \n", "(2025-08-13 16:11:44,772) [INFO]:     [YetAnotherAnomalyDetector] Plot dataset AIOPS score only \n", "(2025-08-13 16:11:53,847) [INFO]:     [YetAnotherAnomalyDetector] Plot dataset WSD score only \n"]}], "source": ["\"\"\"============= Run CosAIService Algorithm =============\"\"\"\n", "# Specifying methods and training schemas\n", "\n", "training_schema = \"naive\"\n", "method = \"YetAnotherAnomalyDetector\"  # \"AnomalyDetector\"  # string of our cos_ai_service algorithm class\n", "\n", "# run models\n", "gctrl.run_exps(\n", "    method=method,\n", "    training_schema=training_schema,\n", "    cfg_path=\"YourAlgo.toml\" # path/to/config\n", ")\n", "    \n", "    \n", "\"\"\"============= [EVALUATION SETTINGS] =============\"\"\"\n", "\n", "from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA\n", "# Specifying evaluation protocols\n", "gctrl.set_evals(\n", "    [\n", "        PointF1PA(),\n", "        EventF1PA(),\n", "        EventF1PA(mode=\"squeeze\")\n", "    ]\n", ")\n", "\n", "gctrl.do_evals(\n", "    method=method,\n", "    training_schema=training_schema\n", ")\n", "    \n", "    \n", "\"\"\"============= [PLOTTING SETTINGS] =============\"\"\"\n", "\n", "# plot anomaly scores for each curve\n", "gctrl.plots(\n", "    method=method,\n", "    training_schema=training_schema\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "eb72d692", "metadata": {}, "outputs": [], "source": ["a = np.load('/Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/Results/Scores/AnomalyDetector/naive/AIOPS/0efb375b-b902-3661-ab23-9a0bb799f4e3.npy', allow_pickle=True)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "0abe461d", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.00509363,  0.00693316,  0.00279188, ..., -0.00495403,\n", "       -0.00204738,  0.00034743])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 10, "id": "76082b03", "metadata": {}, "outputs": [], "source": ["anomaly_score = []\n", "\n", "detector = OnlineAnomalyDetector()\n", "for i in np.array([5,1]):\n", "    score, flag = detector.process_point(i)\n", "    anomaly_score.append(score)"]}, {"cell_type": "code", "execution_count": 11, "id": "fb3b8e8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.0, 4.0]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["anomaly_score"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}