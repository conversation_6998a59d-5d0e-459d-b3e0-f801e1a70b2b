%%% -*-BibTeX-*-
%%% Do NOT edit. File created by BibTeX with style
%%% ACM-Reference-Format-Journals [18-Jan-2012].

\begin{thebibliography}{123}

%%% ====================================================================
%%% NOTE TO THE USER: you can override these defaults by providing
%%% customized versions of any of these macros before the \bibliography
%%% command.  Each of them MUST provide its own final punctuation,
%%% except for \shownote{}, \showDOI{}, and \showURL{}.  The latter two
%%% do not use final punctuation, in order to avoid confusing it with
%%% the Web address.
%%%
%%% To suppress output of a particular field, define its macro to expand
%%% to an empty string, or better, \unskip, like this:
%%%
%%% \newcommand{\showDOI}[1]{\unskip}   % LaTeX syntax
%%%
%%% \def \showDOI #1{\unskip}           % plain TeX syntax
%%%
%%% ====================================================================

\ifx \showCODEN    \undefined \def \showCODEN     #1{\unskip}     \fi
\ifx \showDOI      \undefined \def \showDOI       #1{#1}\fi
\ifx \showISBNx    \undefined \def \showISBNx     #1{\unskip}     \fi
\ifx \showISBNxiii \undefined \def \showISBNxiii  #1{\unskip}     \fi
\ifx \showISSN     \undefined \def \showISSN      #1{\unskip}     \fi
\ifx \showLCCN     \undefined \def \showLCCN      #1{\unskip}     \fi
\ifx \shownote     \undefined \def \shownote      #1{#1}          \fi
\ifx \showarticletitle \undefined \def \showarticletitle #1{#1}   \fi
\ifx \showURL      \undefined \def \showURL       {\relax}        \fi
% The following commands are used for tagged output and should be
% invisible to TeX
\providecommand\bibfield[2]{#2}
\providecommand\bibinfo[2]{#2}
\providecommand\natexlab[1]{#1}
\providecommand\showeprint[2][]{arXiv:#2}

\bibitem[Abdalkareem et~al\mbox{.}(2021a)]%
        {abdalkareem_machine_2021}
\bibfield{author}{\bibinfo{person}{Rabe Abdalkareem}, \bibinfo{person}{Suhaib Mujahid}, {and} \bibinfo{person}{Emad Shihab}.} \bibinfo{year}{2021}\natexlab{a}.
\newblock \showarticletitle{A {Machine} {Learning} {Approach} to {Improve} the {Detection} of {CI} {Skip} {Commits}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{47}, \bibinfo{number}{12} (\bibinfo{year}{2021}), \bibinfo{pages}{2740 -- 2754}.
\newblock
\showISSN{00985589}
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/TSE.2020.2967380}
\showURL{%
\tempurl}


\bibitem[Abdalkareem et~al\mbox{.}(2021b)]%
        {abdalkareem_which_2021}
\bibfield{author}{\bibinfo{person}{Rabe Abdalkareem}, \bibinfo{person}{Suhaib Mujahid}, \bibinfo{person}{Emad Shihab}, {and} \bibinfo{person}{Juergen Rilling}.} \bibinfo{year}{2021}\natexlab{b}.
\newblock \showarticletitle{Which {Commits} {Can} {Be} {CI} {Skipped}?}
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{47}, \bibinfo{number}{3} (\bibinfo{year}{2021}), \bibinfo{pages}{448 -- 463}.
\newblock
\showISSN{00985589}
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/TSE.2019.2897300}
\showURL{%
\tempurl}


\bibitem[Al-Sabbagh et~al\mbox{.}(2022)]%
        {al-sabbagh_predicting_2022}
\bibfield{author}{\bibinfo{person}{Khaled Al-Sabbagh}, \bibinfo{person}{Miroslaw Staron}, {and} \bibinfo{person}{Regina Hebig}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Predicting build outcomes in continuous integration using textual analysis of source code commits}. In \bibinfo{booktitle}{\emph{Proceedings of the 18th {International} {Conference} on {Predictive} {Models} and {Data} {Analytics} in {Software} {Engineering}}} \emph{(\bibinfo{series}{{PROMISE} 2022})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{42--51}.
\newblock
\showISBNx{978-1-4503-9860-2}
\urldef\tempurl%
\url{https://doi.org/10.1145/3558489.3559070}
\showDOI{\tempurl}


\bibitem[Baral et~al\mbox{.}(2023)]%
        {baral_optimizing_2023}
\bibfield{author}{\bibinfo{person}{Talank Baral}, \bibinfo{person}{Shanto Rahman}, \bibinfo{person}{Bala~Naren Chanumolu}, \bibinfo{person}{Başak Balcı}, \bibinfo{person}{Tuna Tuncer}, \bibinfo{person}{August Shi}, {and} \bibinfo{person}{Wing Lam}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{Optimizing {Continuous} {Development} by {Detecting} and {Preventing} {Unnecessary} {Content} {Generation}}. In \bibinfo{booktitle}{\emph{2023 38th {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} ({ASE})}}. \bibinfo{pages}{901--913}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ASE56229.2023.00216}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 2643-1572}.


\bibitem[Barrak et~al\mbox{.}(2021)]%
        {barrak_why_2021}
\bibfield{author}{\bibinfo{person}{Amine Barrak}, \bibinfo{person}{Ellis~E. Eghan}, \bibinfo{person}{Bram Adams}, {and} \bibinfo{person}{Foutse Khomh}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Why do builds fail?—{A} conceptual replication study}.
\newblock \bibinfo{journal}{\emph{Journal of Systems and Software}}  \bibinfo{volume}{177} (\bibinfo{date}{July} \bibinfo{year}{2021}), \bibinfo{pages}{110939}.
\newblock
\showISSN{0164-1212}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.jss.2021.110939}
\showDOI{\tempurl}


\bibitem[Beheshtian et~al\mbox{.}(2022)]%
        {beheshtian_software_2022}
\bibfield{author}{\bibinfo{person}{Mohammad~Javad Beheshtian}, \bibinfo{person}{Amir~Hossein Bavand}, {and} \bibinfo{person}{Peter~C. Rigby}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Software {Batch} {Testing} to {Save} {Build} {Test} {Resources} and to {Reduce} {Feedback} {Time}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{48}, \bibinfo{number}{8} (\bibinfo{year}{2022}), \bibinfo{pages}{2784 -- 2801}.
\newblock
\showISSN{00985589}
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/TSE.2021.3070269}
\showURL{%
\tempurl}


\bibitem[Beller et~al\mbox{.}(2017a)]%
        {beller_oops_2017}
\bibfield{author}{\bibinfo{person}{Moritz Beller}, \bibinfo{person}{Georgios Gousios}, {and} \bibinfo{person}{Andy Zaidman}.} \bibinfo{year}{2017}\natexlab{a}.
\newblock \showarticletitle{Oops, my tests broke the build: an explorative analysis of {Travis} {CI} with {GitHub}}. In \bibinfo{booktitle}{\emph{Proceedings of the 14th {International} {Conference} on {Mining} {Software} {Repositories}}} \emph{(\bibinfo{series}{{MSR} '17})}. \bibinfo{publisher}{IEEE Press}, \bibinfo{address}{Buenos Aires, Argentina}, \bibinfo{pages}{356--367}.
\newblock
\showISBNx{978-1-5386-1544-7}
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.62}
\showDOI{\tempurl}


\bibitem[Beller et~al\mbox{.}(2017b)]%
        {beller_travistorrent_2017}
\bibfield{author}{\bibinfo{person}{Moritz Beller}, \bibinfo{person}{Georgios Gousios}, {and} \bibinfo{person}{Andy Zaidman}.} \bibinfo{year}{2017}\natexlab{b}.
\newblock \showarticletitle{{TravisTorrent}: {Synthesizing} {Travis} {CI} and {GitHub} for {Full}-{Stack} {Research} on {Continuous} {Integration}}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 14th {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{447--450}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.24}
\showDOI{\tempurl}


\bibitem[Benjamin and Mathew(2024)]%
        {benjamin_enhancing_2024}
\bibfield{author}{\bibinfo{person}{Jetty Benjamin} {and} \bibinfo{person}{Juby Mathew}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{Enhancing continuous integration predictions: a hybrid {LSTM}-{GRU} deep learning framework with evolved {DBSO} algorithm}.
\newblock \bibinfo{journal}{\emph{Computing}} \bibinfo{volume}{107}, \bibinfo{number}{1} (\bibinfo{date}{Nov.} \bibinfo{year}{2024}), \bibinfo{pages}{9}.
\newblock
\showISSN{1436-5057}
\urldef\tempurl%
\url{https://doi.org/10.1007/s00607-024-01370-2}
\showDOI{\tempurl}


\bibitem[Benjamin et~al\mbox{.}(2023)]%
        {benjamin_study_2023}
\bibfield{author}{\bibinfo{person}{Jetty Benjamin}, \bibinfo{person}{Juby Mathew}, {and} \bibinfo{person}{Rubin~Thottupurathu Jose}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{Study of the contextual factors of a project affecting the build performance in {Continuous} {Integration}}. In \bibinfo{booktitle}{\emph{2023 {Annual} {International} {Conference} on {Emerging} {Research} {Areas}: {International} {Conference} on {Intelligent} {Systems} ({AICERA}/{ICIS})}}. \bibinfo{pages}{1--6}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/AICERA/ICIS59538.2023.10420309}
\showDOI{\tempurl}


\bibitem[Bifet and Gavaldà(2007)]%
        {bifet_learning_2007}
\bibfield{author}{\bibinfo{person}{Albert Bifet} {and} \bibinfo{person}{Ricard Gavaldà}.} \bibinfo{year}{2007}\natexlab{}.
\newblock \showarticletitle{Learning from {Time}-{Changing} {Data} with {Adaptive} {Windowing}}. In \bibinfo{booktitle}{\emph{Proceedings of the 2007 {SIAM} {International} {Conference} on {Data} {Mining}}}. \bibinfo{publisher}{Society for Industrial and Applied Mathematics}, \bibinfo{pages}{443--448}.
\newblock
\showISBNx{978-0-89871-630-6 978-1-61197-277-1}
\urldef\tempurl%
\url{https://doi.org/10.1137/1.9781611972771.42}
\showDOI{\tempurl}


\bibitem[Bisong et~al\mbox{.}(2017)]%
        {bisong_built_2017}
\bibfield{author}{\bibinfo{person}{Ekaba Bisong}, \bibinfo{person}{Eric Tran}, {and} \bibinfo{person}{Olga Baysal}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Built to {Last} or {Built} {Too} {Fast}? {Evaluating} {Prediction} {Models} for {Build} {Times}}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 14th {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{487--490}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.36}
\showDOI{\tempurl}


\bibitem[Brandt et~al\mbox{.}(2020)]%
        {brandt_logchunks_2020}
\bibfield{author}{\bibinfo{person}{Carolin~E. Brandt}, \bibinfo{person}{Annibale Panichella}, \bibinfo{person}{Andy Zaidman}, {and} \bibinfo{person}{Moritz Beller}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{{LogChunks}: {A} {Data} {Set} for {Build} {Log} {Analysis}}. In \bibinfo{booktitle}{\emph{Proceedings of the 17th {International} {Conference} on {Mining} {Software} {Repositories}}} \emph{(\bibinfo{series}{{MSR} '20})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{583--587}.
\newblock
\showISBNx{978-1-4503-7517-7}
\urldef\tempurl%
\url{https://doi.org/10.1145/3379597.3387485}
\showDOI{\tempurl}


\bibitem[Cao et~al\mbox{.}(2017)]%
        {cao_forecasting_2017}
\bibfield{author}{\bibinfo{person}{Qi Cao}, \bibinfo{person}{Ruiyin Wen}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Forecasting the {Duration} of {Incremental} {Build} {Jobs}}. In \bibinfo{booktitle}{\emph{2017 {IEEE} {International} {Conference} on {Software} {Maintenance} and {Evolution} ({ICSME})}}. \bibinfo{pages}{524--528}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSME.2017.34}
\showDOI{\tempurl}


\bibitem[Celik et~al\mbox{.}(2016)]%
        {celik_build_2016}
\bibfield{author}{\bibinfo{person}{Ahmet Celik}, \bibinfo{person}{Alex Knaust}, \bibinfo{person}{Aleksandar Milicevic}, {and} \bibinfo{person}{Milos Gligoric}.} \bibinfo{year}{2016}\natexlab{}.
\newblock \showarticletitle{Build system with lazy retrieval for {Java} projects}. In \bibinfo{booktitle}{\emph{Proceedings of the 2016 24th {ACM} {SIGSOFT} {International} {Symposium} on {Foundations} of {Software} {Engineering}}} \emph{(\bibinfo{series}{{FSE} 2016})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{643--654}.
\newblock
\showISBNx{978-1-4503-4218-6}
\urldef\tempurl%
\url{https://doi.org/10.1145/2950290.2950358}
\showDOI{\tempurl}


\bibitem[Chen et~al\mbox{.}(2020)]%
        {chen_buildfast_2020}
\bibfield{author}{\bibinfo{person}{Bihuan Chen}, \bibinfo{person}{Linlin Chen}, \bibinfo{person}{Chen Zhang}, {and} \bibinfo{person}{Xin Peng}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{{BUILDFAST}: {History}-{Aware} {Build} {Outcome} {Prediction} for {Fast} {Feedback} and {Reduced} {Cost} in {Continuous} {Integration}}. In \bibinfo{booktitle}{\emph{2020 35th {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} ({ASE})}}. \bibinfo{address}{Los Alamitos, CA, USA}, \bibinfo{pages}{42 -- 53}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3324884.3416616}
\showURL{%
\tempurl}


\bibitem[Chen et~al\mbox{.}(2006)]%
        {chen_bellwether_2006}
\bibfield{author}{\bibinfo{person}{Bee-Chung Chen}, \bibinfo{person}{Raghu Ramakrishnan}, \bibinfo{person}{Jude~W. Shavlik}, {and} \bibinfo{person}{Pradeep Tamma}.} \bibinfo{year}{2006}\natexlab{}.
\newblock \showarticletitle{Bellwether analysis: predicting global aggregates from local regions}.
\newblock \bibinfo{journal}{\emph{Proceedings of the 32nd international conference on Very large data bases}} (\bibinfo{date}{Sept.} \bibinfo{year}{2006}), \bibinfo{pages}{655--666}.
\newblock


\bibitem[Corbin and Strauss(2008)]%
        {corbin_basics_2008}
\bibfield{author}{\bibinfo{person}{Juliet Corbin} {and} \bibinfo{person}{Anselm Strauss}.} \bibinfo{year}{2008}\natexlab{}.
\newblock \bibinfo{booktitle}{\emph{Basics of {Qualitative} {Research} (3rd ed.): {Techniques} and {Procedures} for {Developing} {Grounded} {Theory}}}.
\newblock \bibinfo{publisher}{SAGE Publications, Inc.}
\newblock
\showISBNx{978-1-4522-3015-3}
\urldef\tempurl%
\url{https://doi.org/10.4135/9781452230153}
\showDOI{\tempurl}


\bibitem[Deb et~al\mbox{.}(2002)]%
        {deb_fast_2002}
\bibfield{author}{\bibinfo{person}{K. Deb}, \bibinfo{person}{A. Pratap}, \bibinfo{person}{S. Agarwal}, {and} \bibinfo{person}{T. Meyarivan}.} \bibinfo{year}{2002}\natexlab{}.
\newblock \showarticletitle{A fast and elitist multiobjective genetic algorithm: {NSGA}-{II}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Evolutionary Computation}} \bibinfo{volume}{6}, \bibinfo{number}{2} (\bibinfo{date}{April} \bibinfo{year}{2002}), \bibinfo{pages}{182--197}.
\newblock
\showISSN{1941-0026}
\urldef\tempurl%
\url{https://doi.org/10.1109/4235.996017}
\showDOI{\tempurl}
\newblock
\shownote{Conference Name: IEEE Transactions on Evolutionary Computation}.


\bibitem[Dimitropoulos et~al\mbox{.}(2017)]%
        {dimitropoulos_continuous_2017}
\bibfield{author}{\bibinfo{person}{Panagiotis Dimitropoulos}, \bibinfo{person}{Zeyar Aung}, {and} \bibinfo{person}{Davor Svetinovic}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Continuous integration build breakage rationale: {Travis} data case study}. In \bibinfo{booktitle}{\emph{2017 {International} {Conference} on {Infocom} {Technologies} and {Unmanned} {Systems} ({Trends} and {Future} {Directions}) ({ICTUS})}}. \bibinfo{pages}{639--645}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICTUS.2017.8286087}
\showDOI{\tempurl}


\bibitem[El-Masri et~al\mbox{.}(2020)]%
        {el-masri_systematic_2020}
\bibfield{author}{\bibinfo{person}{Diana El-Masri}, \bibinfo{person}{Fabio Petrillo}, \bibinfo{person}{Yann-Gaël Guéhéneuc}, \bibinfo{person}{Abdelwahab Hamou-Lhadj}, {and} \bibinfo{person}{Anas Bouziane}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{A systematic literature review on automated log abstraction techniques}.
\newblock \bibinfo{journal}{\emph{Information and Software Technology}}  \bibinfo{volume}{122} (\bibinfo{date}{June} \bibinfo{year}{2020}), \bibinfo{pages}{106276}.
\newblock
\showISSN{0950-5849}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.infsof.2020.106276}
\showDOI{\tempurl}


\bibitem[Elazhary et~al\mbox{.}(2022)]%
        {elazhary_uncovering_2022}
\bibfield{author}{\bibinfo{person}{Omar Elazhary}, \bibinfo{person}{Colin Werner}, \bibinfo{person}{Ze~Shi Li}, \bibinfo{person}{Derek Lowlind}, \bibinfo{person}{Neil Ernst}, {and} \bibinfo{person}{Margaret-Anne Storey}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Uncovering the {Benefits} and {Challenges} of {Continuous} {Integration} {Practices}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{48}, \bibinfo{number}{7} (\bibinfo{date}{July} \bibinfo{year}{2022}), \bibinfo{pages}{2570--2583}.
\newblock
\showISSN{0098-5589, 1939-3520, 2326-3881}
\urldef\tempurl%
\url{https://doi.org/10.1109/TSE.2021.3064953}
\showDOI{\tempurl}
\newblock
\shownote{arXiv:2103.04251 [cs]}.


\bibitem[{Elsevier}(2022)]%
        {elsevier_engineering_2022}
\bibfield{author}{\bibinfo{person}{{Elsevier}}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \bibinfo{title}{Engineering {Websites} {Index} \& {Journals} {Database}}.
\newblock
\newblock
\urldef\tempurl%
\url{https://www.elsevier.com/solutions/engineering-village/databases}
\showURL{%
\tempurl}


\bibitem[Esfahani et~al\mbox{.}(2016)]%
        {esfahani_cloudbuild_2016}
\bibfield{author}{\bibinfo{person}{Hamed Esfahani}, \bibinfo{person}{Jonas Fietz}, \bibinfo{person}{Qi Ke}, \bibinfo{person}{Alexei Kolomiets}, \bibinfo{person}{Erica Lan}, \bibinfo{person}{Erik Mavrinac}, \bibinfo{person}{Wolfram Schulte}, \bibinfo{person}{Newton Sanches}, {and} \bibinfo{person}{Srikanth Kandula}.} \bibinfo{year}{2016}\natexlab{}.
\newblock \showarticletitle{{CloudBuild}: {Microsoft}'s distributed and caching build service}. In \bibinfo{booktitle}{\emph{Proceedings of the 38th {International} {Conference} on {Software} {Engineering} {Companion}}} \emph{(\bibinfo{series}{{ICSE} '16})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{11--20}.
\newblock
\showISBNx{978-1-4503-4205-6}
\urldef\tempurl%
\url{https://doi.org/10.1145/2889160.2889222}
\showDOI{\tempurl}


\bibitem[Felizardo et~al\mbox{.}(2016)]%
        {felizardo_using_2016}
\bibfield{author}{\bibinfo{person}{Katia~Romero Felizardo}, \bibinfo{person}{Emilia Mendes}, \bibinfo{person}{Marcos Kalinowski}, \bibinfo{person}{Érica~Ferreira Souza}, {and} \bibinfo{person}{Nandamudi~L. Vijaykumar}.} \bibinfo{year}{2016}\natexlab{}.
\newblock \showarticletitle{Using {Forward} {Snowballing} to update {Systematic} {Reviews} in {Software} {Engineering}}. In \bibinfo{booktitle}{\emph{Proceedings of the 10th {ACM}/{IEEE} {International} {Symposium} on {Empirical} {Software} {Engineering} and {Measurement}}} \emph{(\bibinfo{series}{{ESEM} '16})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1--6}.
\newblock
\showISBNx{978-1-4503-4427-2}
\urldef\tempurl%
\url{https://doi.org/10.1145/2961111.2962630}
\showDOI{\tempurl}


\bibitem[Finlay et~al\mbox{.}(2014)]%
        {finlay_data_2014}
\bibfield{author}{\bibinfo{person}{Jacqui Finlay}, \bibinfo{person}{Russel Pears}, {and} \bibinfo{person}{Andy~M. Connor}.} \bibinfo{year}{2014}\natexlab{}.
\newblock \showarticletitle{Data stream mining for predicting software build outcomes using source code metrics}.
\newblock \bibinfo{journal}{\emph{Information and Software Technology}} \bibinfo{volume}{56}, \bibinfo{number}{2} (\bibinfo{date}{Feb.} \bibinfo{year}{2014}), \bibinfo{pages}{183--198}.
\newblock
\showISSN{0950-5849}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.infsof.2013.09.001}
\showDOI{\tempurl}


\bibitem[Gallaba et~al\mbox{.}(2022a)]%
        {gallaba_accelerating_2022}
\bibfield{author}{\bibinfo{person}{Keheliya Gallaba}, \bibinfo{person}{John Ewart}, \bibinfo{person}{Yves Junqueira}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2022}\natexlab{a}.
\newblock \showarticletitle{Accelerating {Continuous} {Integration} by {Caching} {Environments} and {Inferring} {Dependencies}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{48}, \bibinfo{number}{6} (\bibinfo{year}{2022}), \bibinfo{pages}{2040 -- 2052}.
\newblock
\showISSN{00985589}
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/TSE.2020.3048335}
\showURL{%
\tempurl}


\bibitem[Gallaba et~al\mbox{.}(2022b)]%
        {gallaba_lessons_2022}
\bibfield{author}{\bibinfo{person}{Keheliya Gallaba}, \bibinfo{person}{Maxime Lamothe}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2022}\natexlab{b}.
\newblock \showarticletitle{Lessons from {Eight} {Years} of {Operational} {Data} from a {Continuous} {Integration} {Service}: {An} {Exploratory} {Case} {Study} of {CircleCI}}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}, Vol.~\bibinfo{volume}{2022-May}. \bibinfo{address}{Pittsburgh, PA, United states}, \bibinfo{pages}{1330 -- 1342}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3510003.3510211}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Gallaba et~al\mbox{.}(2018)]%
        {gallaba_noise_2018}
\bibfield{author}{\bibinfo{person}{Keheliya Gallaba}, \bibinfo{person}{Christian Macho}, \bibinfo{person}{Martin Pinzger}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Noise and heterogeneity in historical build data: an empirical study of {Travis} {CI}}. In \bibinfo{booktitle}{\emph{Proceedings of the 33rd {ACM}/{IEEE} {International} {Conference} on {Automated} {Software} {Engineering}}} \emph{(\bibinfo{series}{{ASE} 2018})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{87--97}.
\newblock
\showISBNx{978-1-4503-5937-5}
\urldef\tempurl%
\url{https://doi.org/10.1145/3238147.3238171}
\showDOI{\tempurl}


\bibitem[Gezici and Tarhan(2022)]%
        {gezici_systematic_2022}
\bibfield{author}{\bibinfo{person}{Bahar Gezici} {and} \bibinfo{person}{Ayça~Kolukısa Tarhan}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Systematic literature review on software quality for {AI}-based software}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{27}, \bibinfo{number}{3} (\bibinfo{date}{March} \bibinfo{year}{2022}), \bibinfo{pages}{66}.
\newblock
\showISSN{1573-7616}
\urldef\tempurl%
\url{https://doi.org/10.1007/s10664-021-10105-2}
\showDOI{\tempurl}


\bibitem[Ghaleb et~al\mbox{.}(2019)]%
        {ghaleb_empirical_2019}
\bibfield{author}{\bibinfo{person}{T.A. Ghaleb}, \bibinfo{person}{D.A. da Costa}, {and} \bibinfo{person}{Ying Zou}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{An empirical study of the long duration of continuous integration builds}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{24}, \bibinfo{number}{4} (\bibinfo{date}{Aug.} \bibinfo{year}{2019}), \bibinfo{pages}{2102 -- 39}.
\newblock
\showISSN{1382-3256}
\urldef\tempurl%
\url{http://dx.doi.org/10.1007/s10664-019-09695-9}
\showURL{%
\tempurl}
\newblock
\shownote{Place: Germany}.


\bibitem[Ghaleb et~al\mbox{.}(2021)]%
        {ghaleb_studying_2021}
\bibfield{author}{\bibinfo{person}{Taher~Ahmed Ghaleb}, \bibinfo{person}{Daniel~Alencar da Costa}, \bibinfo{person}{Ying Zou}, {and} \bibinfo{person}{Ahmed~E. Hassan}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Studying the {Impact} of {Noises} in {Build} {Breakage} {Data}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{47}, \bibinfo{number}{9} (\bibinfo{date}{Sept.} \bibinfo{year}{2021}), \bibinfo{pages}{1998--2011}.
\newblock
\showISSN{1939-3520}
\urldef\tempurl%
\url{https://doi.org/10.1109/TSE.2019.2941880}
\showDOI{\tempurl}
\newblock
\shownote{Conference Name: IEEE Transactions on Software Engineering}.


\bibitem[Ghaleb et~al\mbox{.}(2022)]%
        {ghaleb_studying_2022}
\bibfield{author}{\bibinfo{person}{Taher~A. Ghaleb}, \bibinfo{person}{Safwat Hassan}, {and} \bibinfo{person}{Ying Zou}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Studying the {Interplay} between the {Durations} and {Breakages} of {Continuous} {Integration} {Builds}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} (\bibinfo{year}{2022}), \bibinfo{pages}{1--21}.
\newblock
\showISSN{1939-3520}
\urldef\tempurl%
\url{https://doi.org/10.1109/TSE.2022.3222160}
\showDOI{\tempurl}
\newblock
\shownote{Conference Name: IEEE Transactions on Software Engineering}.


\bibitem[Golzadeh et~al\mbox{.}(2022)]%
        {golzadeh_rise_2022}
\bibfield{author}{\bibinfo{person}{Mehdi Golzadeh}, \bibinfo{person}{Alexandre Decan}, {and} \bibinfo{person}{Tom Mens}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{On the rise and fall of {CI} services in {GitHub}}. In \bibinfo{booktitle}{\emph{2022 {IEEE} {International} {Conference} on {Software} {Analysis}, {Evolution} and {Reengineering} ({SANER})}}. \bibinfo{pages}{662--672}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/SANER53432.2022.00084}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 1534-5351}.


\bibitem[Hassan and Zhang(2006)]%
        {hassan_using_2006}
\bibfield{author}{\bibinfo{person}{Ahmed~E. Hassan} {and} \bibinfo{person}{Ken Zhang}.} \bibinfo{year}{2006}\natexlab{}.
\newblock \showarticletitle{Using {Decision} {Trees} to {Predict} the {Certification} {Result} of a {Build}}. In \bibinfo{booktitle}{\emph{21st {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} ({ASE}'06)}}. \bibinfo{pages}{189--198}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ASE.2006.72}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 1938-4300}.


\bibitem[Hassan(2019)]%
        {hassan_tackling_2019}
\bibfield{author}{\bibinfo{person}{F. Hassan}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{Tackling {Build} {Failures} in {Continuous} {Integration}}. In \bibinfo{booktitle}{\emph{2019 34th {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} ({ASE}). {Proceedings}}}. \bibinfo{address}{Los Alamitos, CA, USA}, \bibinfo{pages}{1242 -- 5}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/ASE.2019.00150}
\showURL{%
\tempurl}


\bibitem[Hassan et~al\mbox{.}(2023)]%
        {hassan_uniloc_2023}
\bibfield{author}{\bibinfo{person}{Foyzul Hassan}, \bibinfo{person}{Na Meng}, {and} \bibinfo{person}{Xiaoyin Wang}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{{UniLoc}: {Unified} {Fault} {Localization} of {Continuous} {Integration} {Failures}}.
\newblock \bibinfo{journal}{\emph{ACM Trans. Softw. Eng. Methodol.}} \bibinfo{volume}{32}, \bibinfo{number}{6} (\bibinfo{date}{Sept.} \bibinfo{year}{2023}), \bibinfo{pages}{136:1--136:31}.
\newblock
\showISSN{1049-331X}
\urldef\tempurl%
\url{https://doi.org/10.1145/3593799}
\showDOI{\tempurl}


\bibitem[Hassan and Wang(2017)]%
        {hassan_change-aware_2017}
\bibfield{author}{\bibinfo{person}{Foyzul Hassan} {and} \bibinfo{person}{Xiaoyin Wang}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Change-{Aware} {Build} {Prediction} {Model} for {Stall} {Avoidance} in {Continuous} {Integration}}. In \bibinfo{booktitle}{\emph{International {Symposium} on {Empirical} {Software} {Engineering} and {Measurement}}}, Vol.~\bibinfo{volume}{2017-November}. \bibinfo{address}{Toronto, ON, Canada}, \bibinfo{pages}{157 -- 162}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/ESEM.2017.23}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 19493770}.


\bibitem[Hassan and Wang(2018)]%
        {hassan_hirebuild_2018}
\bibfield{author}{\bibinfo{person}{Foyzul Hassan} {and} \bibinfo{person}{Xiaoyin Wang}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{{HireBuild}: an automatic approach to history-driven repair of build scripts}. In \bibinfo{booktitle}{\emph{Proceedings of the 40th {International} {Conference} on {Software} {Engineering}}} \emph{(\bibinfo{series}{{ICSE} '18})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1078--1089}.
\newblock
\showISBNx{978-1-4503-5638-1}
\urldef\tempurl%
\url{https://doi.org/10.1145/3180155.3180181}
\showDOI{\tempurl}


\bibitem[Henkel et~al\mbox{.}(2020)]%
        {henkel_learning_2020}
\bibfield{author}{\bibinfo{person}{Jordan Henkel}, \bibinfo{person}{Christian Bird}, \bibinfo{person}{Shuvendu~K. Lahiri}, {and} \bibinfo{person}{Thomas Reps}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Learning from, understanding, and supporting devops artifacts for docker}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Virtual, Online, Korea, Republic of}, \bibinfo{pages}{38 -- 49}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3377811.3380406}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Hills(2020)]%
        {hills_introducing_2020}
\bibfield{author}{\bibinfo{person}{M. Hills}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Introducing {DevOps} {Techniques} in a {Software} {Construction} {Class}}. In \bibinfo{booktitle}{\emph{2020 {IEEE} 32nd {Conference} on {Software} {Engineering} {Education} and {Training} ({CSEE}\&amp;{T})}}. \bibinfo{address}{Piscataway, NJ, USA}, \bibinfo{pages}{5 pp. --}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/CSEET49119.2020.9206183}
\showURL{%
\tempurl}


\bibitem[Hilton et~al\mbox{.}(2017)]%
        {hilton_trade-offs_2017}
\bibfield{author}{\bibinfo{person}{Michael Hilton}, \bibinfo{person}{Nicholas Nelson}, \bibinfo{person}{Timothy Tunnell}, \bibinfo{person}{Darko Marinov}, {and} \bibinfo{person}{Danny Dig}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Trade-offs in continuous integration: assurance, security, and flexibility}. In \bibinfo{booktitle}{\emph{Proceedings of the 2017 11th {Joint} {Meeting} on {Foundations} of {Software} {Engineering}}} \emph{(\bibinfo{series}{{ESEC}/{FSE} 2017})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{197--207}.
\newblock
\showISBNx{978-1-4503-5105-8}
\urldef\tempurl%
\url{https://doi.org/10.1145/3106237.3106270}
\showDOI{\tempurl}


\bibitem[Hilton et~al\mbox{.}(2016)]%
        {hilton_usage_2016}
\bibfield{author}{\bibinfo{person}{Michael Hilton}, \bibinfo{person}{Timothy Tunnell}, \bibinfo{person}{Kai Huang}, \bibinfo{person}{Darko Marinov}, {and} \bibinfo{person}{Danny Dig}.} \bibinfo{year}{2016}\natexlab{}.
\newblock \showarticletitle{Usage, costs, and benefits of continuous integration in open-source projects}. In \bibinfo{booktitle}{\emph{2016 31st {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} ({ASE})}}. \bibinfo{pages}{426--437}.
\newblock


\bibitem[Hong et~al\mbox{.}(2024)]%
        {hong_practitioners_2024}
\bibfield{author}{\bibinfo{person}{Yang Hong}, \bibinfo{person}{Chakkrit Tantithamthavorn}, \bibinfo{person}{Jirat Pasuksmit}, \bibinfo{person}{Patanamon Thongtanunam}, \bibinfo{person}{Arik Friedman}, \bibinfo{person}{Xing Zhao}, {and} \bibinfo{person}{Anton Krasikov}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{Practitioners’ {Challenges} and {Perceptions} of {CI} {Build} {Failure} {Predictions} at {Atlassian}}. In \bibinfo{booktitle}{\emph{Companion {Proceedings} of the 32nd {ACM} {International} {Conference} on the {Foundations} of {Software} {Engineering}}} \emph{(\bibinfo{series}{{FSE} 2024})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{370--381}.
\newblock
\showISBNx{9798400706585}
\urldef\tempurl%
\url{https://doi.org/10.1145/3663529.3663856}
\showDOI{\tempurl}


\bibitem[Hulten et~al\mbox{.}(2001)]%
        {hulten_mining_2001}
\bibfield{author}{\bibinfo{person}{Geoff Hulten}, \bibinfo{person}{Laurie Spencer}, {and} \bibinfo{person}{Pedro Domingos}.} \bibinfo{year}{2001}\natexlab{}.
\newblock \showarticletitle{Mining time-changing data streams}. In \bibinfo{booktitle}{\emph{Proceedings of the seventh {ACM} {SIGKDD} international conference on {Knowledge} discovery and data mining}} \emph{(\bibinfo{series}{{KDD} '01})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{97--106}.
\newblock
\showISBNx{978-1-58113-391-2}
\urldef\tempurl%
\url{https://doi.org/10.1145/502512.502529}
\showDOI{\tempurl}


\bibitem[Islam and Zibran(2017)]%
        {islam_insights_2017}
\bibfield{author}{\bibinfo{person}{Md~Rakibul Islam} {and} \bibinfo{person}{Minhaz~F. Zibran}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Insights into {Continuous} {Integration} {Build} {Failures}}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 14th {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{467--470}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.30}
\showDOI{\tempurl}


\bibitem[Jin(2021)]%
        {jin_reducing_2021}
\bibfield{author}{\bibinfo{person}{Xianhao Jin}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Reducing cost in continuous integration with a collection of build selection approaches}. In \bibinfo{booktitle}{\emph{{ESEC}/{FSE} 2021: {Proceedings} of the 29th {ACM} {Joint} {Meeting} on {European} {Software} {Engineering} {Conference} and {Symposium} on the {Foundations} of {Software} {Engineering}}}. \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1650 -- 4}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3468264.3473103}
\showURL{%
\tempurl}


\bibitem[Jin and Servant(2020)]%
        {jin_cost-efficient_2020}
\bibfield{author}{\bibinfo{person}{Xianhao Jin} {and} \bibinfo{person}{F. Servant}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{A cost-efficient approach to building in continuous integration}. In \bibinfo{booktitle}{\emph{2020 {IEEE}/{ACM} 42nd {International} {Conference} on {Software} {Engineering} ({ICSE})}}. \bibinfo{address}{Los Alamitos, CA, USA}, \bibinfo{pages}{13 -- 25}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3377811.3380437}
\showURL{%
\tempurl}


\bibitem[Jin and Servant(2021a)]%
        {jin_cibench_2021}
\bibfield{author}{\bibinfo{person}{Xianhao Jin} {and} \bibinfo{person}{Francisco Servant}.} \bibinfo{year}{2021}\natexlab{a}.
\newblock \showarticletitle{{CIBench}: {A} {Dataset} and {Collection} of {Techniques} for {Build} and {Test} {Selection} and {Prioritization} in {Continuous} {Integration}}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Virtual, Online, Spain}, \bibinfo{pages}{166 -- 167}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/ICSE-Companion52605.2021.00070}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Jin and Servant(2021b)]%
        {jin_what_2021}
\bibfield{author}{\bibinfo{person}{Xianhao Jin} {and} \bibinfo{person}{Francisco Servant}.} \bibinfo{year}{2021}\natexlab{b}.
\newblock \showarticletitle{What helped, and what did not? {An} evaluation of the strategies to improve continuous integration}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Virtual, Online, Spain}, \bibinfo{pages}{213 -- 225}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/ICSE43902.2021.00031}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Jin and Servant(2022)]%
        {jin_which_2022}
\bibfield{author}{\bibinfo{person}{Xianhao Jin} {and} \bibinfo{person}{Francisco Servant}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Which builds are really safe to skip? {Maximizing} failure observation for build selection in continuous integration}.
\newblock \bibinfo{journal}{\emph{Journal of Systems and Software}}  \bibinfo{volume}{188} (\bibinfo{date}{June} \bibinfo{year}{2022}), \bibinfo{pages}{111292}.
\newblock
\showISSN{01641212}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.jss.2022.111292}
\showDOI{\tempurl}


\bibitem[Jin and Servant(2023)]%
        {jin_span_2023}
\bibfield{author}{\bibinfo{person}{Xianhao Jin} {and} \bibinfo{person}{Francisco Servant}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{{\textless}span style="font-variant:small-caps;"{\textgreater}{HybridCISave}{\textless}/span{\textgreater} : {A} {Combined} {Build} and {Test} {Selection} {Approach} in {Continuous} {Integration}}.
\newblock \bibinfo{journal}{\emph{ACM Transactions on Software Engineering and Methodology}} \bibinfo{volume}{32}, \bibinfo{number}{4} (\bibinfo{date}{Oct.} \bibinfo{year}{2023}), \bibinfo{pages}{1--39}.
\newblock
\showISSN{1049-331X, 1557-7392}
\urldef\tempurl%
\url{https://doi.org/10.1145/3576038}
\showDOI{\tempurl}


\bibitem[Kamath et~al\mbox{.}(2024)]%
        {kamath_combining_2024}
\bibfield{author}{\bibinfo{person}{Divya~M. Kamath}, \bibinfo{person}{Eduardo Fernandes}, \bibinfo{person}{Bram Adams}, {and} \bibinfo{person}{Ahmed~E. Hassan}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{On combining commit grouping and build skip prediction to reduce redundant continuous integration activity}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{29}, \bibinfo{number}{6} (\bibinfo{date}{Aug.} \bibinfo{year}{2024}), \bibinfo{pages}{145}.
\newblock
\showISSN{1573-7616}
\urldef\tempurl%
\url{https://doi.org/10.1007/s10664-024-10477-1}
\showDOI{\tempurl}


\bibitem[Kawalerowicz and Madeyski(2023)]%
        {kawalerowicz_continuous_2023}
\bibfield{author}{\bibinfo{person}{Marcin Kawalerowicz} {and} \bibinfo{person}{Lech Madeyski}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{Continuous build outcome prediction: an experimental evaluation and acceptance modelling}.
\newblock \bibinfo{journal}{\emph{Applied Intelligence}} \bibinfo{volume}{53}, \bibinfo{number}{8} (\bibinfo{date}{April} \bibinfo{year}{2023}), \bibinfo{pages}{8673--8692}.
\newblock
\showISSN{1573-7497}
\urldef\tempurl%
\url{https://doi.org/10.1007/s10489-023-04523-6}
\showDOI{\tempurl}


\bibitem[Kerzazi et~al\mbox{.}(2014)]%
        {kerzazi_why_2014}
\bibfield{author}{\bibinfo{person}{Noureddine Kerzazi}, \bibinfo{person}{Foutse Khomh}, {and} \bibinfo{person}{Bram Adams}.} \bibinfo{year}{2014}\natexlab{}.
\newblock \showarticletitle{Why {Do} {Automated} {Builds} {Break}? {An} {Empirical} {Study}}. In \bibinfo{booktitle}{\emph{Proceedings of the 2014 {IEEE} {International} {Conference} on {Software} {Maintenance} and {Evolution}}} \emph{(\bibinfo{series}{{ICSME} '14})}. \bibinfo{publisher}{IEEE Computer Society}, \bibinfo{address}{USA}, \bibinfo{pages}{41--50}.
\newblock
\showISBNx{978-1-4799-6146-7}
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSME.2014.26}
\showDOI{\tempurl}


\bibitem[Khatami et~al\mbox{.}(2024)]%
        {khatami_catching_2024}
\bibfield{author}{\bibinfo{person}{Ali Khatami}, \bibinfo{person}{Cćdric Willekens}, {and} \bibinfo{person}{Andy Zaidman}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \bibinfo{title}{Catching {Smells} in the {Act}: {A} {GitHub} {Actions} {Workflow} {Investigation} {\textbar} {IEEE} {Conference} {Publication} {\textbar} {IEEE} {Xplore}}.
\newblock
\newblock
\urldef\tempurl%
\url{https://ieeexplore.ieee.org/abstract/document/10795325?casa_token=CcuT9brDk30AAAAA:TAg92khqkMVFluxrDbCwnuucyPYb93MYFSTIkAaFTjEIvRx5EKnoqmlOuGa7PpJjnTg3JJ9YZyhQ}
\showURL{%
\tempurl}


\bibitem[Kitchenham and Charters(2007)]%
        {kitchenham_guidelines_2007}
\bibfield{author}{\bibinfo{person}{Barbara Kitchenham} {and} \bibinfo{person}{Stuart Charters}.} \bibinfo{year}{2007}\natexlab{}.
\newblock \showarticletitle{Guidelines for performing systematic literature reviews in software engineering}.
\newblock  (\bibinfo{year}{2007}).
\newblock
\urldef\tempurl%
\url{https://scholar.google.com/scholar_lookup?title=Guidelines%20for%20performing%20systematic%20literature%20review%20in%20software%20engineering%2C%20Technical%20report%20EBSE-2007-001%2C%20UK&author=B.A.%20Kitchenham&publication_year=2007}
\showURL{%
\tempurl}
\newblock
\shownote{Publisher: Citeseer}.


\bibitem[Kola-Olawuyi et~al\mbox{.}(2024)]%
        {kola-olawuyi_impact_2024}
\bibfield{author}{\bibinfo{person}{Ajiromola Kola-Olawuyi}, \bibinfo{person}{Nimmi~Rashinika Weeraddana}, {and} \bibinfo{person}{Meiyappan Nagappan}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{The {Impact} of {Code} {Ownership} of {DevOps} {Artefacts} on the {Outcome} of {DevOps} {CI} {Builds}}. In \bibinfo{booktitle}{\emph{2024 {IEEE}/{ACM} 21st {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{543--555}.
\newblock
\urldef\tempurl%
\url{https://ieeexplore.ieee.org/abstract/document/10555822/authors#authors}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 2574-3864}.


\bibitem[Kuhn et~al\mbox{.}(2012)]%
        {kuhn_cubist_2012}
\bibfield{author}{\bibinfo{person}{Max Kuhn}, \bibinfo{person}{Steve Weston}, \bibinfo{person}{Chris Keefer}, {and} \bibinfo{person}{Nathan Coulter}.} \bibinfo{year}{2012}\natexlab{}.
\newblock \showarticletitle{Cubist {Models} {For} {Regression}}.
\newblock  (\bibinfo{date}{May} \bibinfo{year}{2012}).
\newblock


\bibitem[Lampel et~al\mbox{.}(2021)]%
        {lampel_when_2021}
\bibfield{author}{\bibinfo{person}{J. Lampel}, \bibinfo{person}{S. Just}, \bibinfo{person}{S. Apel}, {and} \bibinfo{person}{A. Zeller}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{When life gives you oranges: detecting and diagnosing intermittent job failures at {Mozilla}}. In \bibinfo{booktitle}{\emph{{ESEC}/{FSE} 2021: {Proceedings} of the 29th {ACM} {Joint} {Meeting} on {European} {Software} {Engineering} {Conference} and {Symposium} on the {Foundations} of {Software} {Engineering}}}. \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1381 -- 92}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3468264.3473931}
\showURL{%
\tempurl}


\bibitem[Laukkanen et~al\mbox{.}(2017)]%
        {laukkanen_problems_2017}
\bibfield{author}{\bibinfo{person}{Eero Laukkanen}, \bibinfo{person}{Juha Itkonen}, {and} \bibinfo{person}{Casper Lassenius}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Problems, causes and solutions when adopting continuous delivery—{A} systematic literature review}.
\newblock \bibinfo{journal}{\emph{Information and Software Technology}}  \bibinfo{volume}{82} (\bibinfo{date}{Feb.} \bibinfo{year}{2017}), \bibinfo{pages}{55--79}.
\newblock
\showISSN{0950-5849}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.infsof.2016.10.001}
\showDOI{\tempurl}


\bibitem[Laukkanen and Mäntylä(2015)]%
        {laukkanen_build_2015}
\bibfield{author}{\bibinfo{person}{Eero Laukkanen} {and} \bibinfo{person}{Mika Mäntylä}.} \bibinfo{year}{2015}\natexlab{}.
\newblock \showarticletitle{Build {Waiting} {Time} in {Continuous} {Integration} – {An} {Initial} {Interdisciplinary} {Literature} {Review}}. In \bibinfo{booktitle}{\emph{2015 {IEEE}/{ACM} 2nd {International} {Workshop} on {Rapid} {Continuous} {Software} {Engineering}}}. \bibinfo{pages}{1--4}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/RCoSE.2015.8}
\showDOI{\tempurl}


\bibitem[Lebeuf et~al\mbox{.}(2018)]%
        {lebeuf_understanding_2018}
\bibfield{author}{\bibinfo{person}{Carlene Lebeuf}, \bibinfo{person}{Elena Voyloshnikova}, \bibinfo{person}{Kim Herzig}, {and} \bibinfo{person}{Margaret-Anne Storey}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Understanding, {Debugging}, and {Optimizing} {Distributed} {Software} {Builds}: {A} {Design} {Study}}. In \bibinfo{booktitle}{\emph{2018 {IEEE} {International} {Conference} on {Software} {Maintenance} and {Evolution} ({ICSME})}}. \bibinfo{pages}{496--507}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSME.2018.00060}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 2576-3148}.


\bibitem[Lee et~al\mbox{.}(2024)]%
        {lee_applying_2024}
\bibfield{author}{\bibinfo{person}{Jonathan Lee}, \bibinfo{person}{Mason Li}, {and} \bibinfo{person}{Kuo-Hsun Hsu}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{Applying {Transformer} {Models} for {Automatic} {Build} {Errors} {Classification} of {Java}-{Based} {Open} {Source} {Projects}}. In \bibinfo{booktitle}{\emph{Proceedings of the 2024 {IEEE}/{ACM} 46th {International} {Conference} on {Software} {Engineering}: {Companion} {Proceedings}}} \emph{(\bibinfo{series}{{ICSE}-{Companion} '24})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{282--283}.
\newblock
\showISBNx{9798400705021}
\urldef\tempurl%
\url{https://doi.org/10.1145/3639478.3643068}
\showDOI{\tempurl}


\bibitem[Liang et~al\mbox{.}(2018)]%
        {liang_redefining_2018}
\bibfield{author}{\bibinfo{person}{Jingjing Liang}, \bibinfo{person}{Sebastian Elbaum}, {and} \bibinfo{person}{Gregg Rothermel}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Redefining prioritization: {Continuous} prioritization for continuous integration}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Gothenburg, Sweden}, \bibinfo{pages}{688 -- 698}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3180155.3180213}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Lou et~al\mbox{.}(2019)]%
        {lou_history-driven_2019}
\bibfield{author}{\bibinfo{person}{Yiling Lou}, \bibinfo{person}{Junjie Chen}, \bibinfo{person}{Lingming Zhang}, \bibinfo{person}{Dan Hao}, {and} \bibinfo{person}{Lu Zhang}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{History-driven build failure fixing: how far are we?}. In \bibinfo{booktitle}{\emph{Proceedings of the 28th {ACM} {SIGSOFT} {International} {Symposium} on {Software} {Testing} and {Analysis}}} \emph{(\bibinfo{series}{{ISSTA} 2019})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{43--54}.
\newblock
\showISBNx{978-1-4503-6224-5}
\urldef\tempurl%
\url{https://doi.org/10.1145/3293882.3330578}
\showDOI{\tempurl}


\bibitem[Lou et~al\mbox{.}(2020)]%
        {lou_understanding_2020}
\bibfield{author}{\bibinfo{person}{Yiling Lou}, \bibinfo{person}{Zhenpeng Chen}, \bibinfo{person}{Yanbin Cao}, \bibinfo{person}{Dan Hao}, {and} \bibinfo{person}{Lu Zhang}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Understanding build issue resolution in practice: symptoms and fix patterns}. In \bibinfo{booktitle}{\emph{{ESEC}/{FSE} 2020: {Proceedings} of the 28th {ACM} {Joint} {Meeting} on {European} {Software} {Engineering} {Conference} and {Symposium} on the {Foundations} of {Software} {Engineering}}}. \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{617 -- 28}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3368089.3409760}
\showURL{%
\tempurl}


\bibitem[Luo et~al\mbox{.}(2017)]%
        {luo_what_2017}
\bibfield{author}{\bibinfo{person}{Yang Luo}, \bibinfo{person}{Yangyang Zhao}, \bibinfo{person}{Wanwangying Ma}, {and} \bibinfo{person}{Lin Chen}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{What are the {Factors} {Impacting} {Build} {Breakage}?}. In \bibinfo{booktitle}{\emph{2017 14th {Web} {Information} {Systems} and {Applications} {Conference} ({WISA})}}. \bibinfo{pages}{139--142}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/WISA.2017.17}
\showDOI{\tempurl}


\bibitem[Macho et~al\mbox{.}(2016)]%
        {macho_predicting_2016}
\bibfield{author}{\bibinfo{person}{Christian Macho}, \bibinfo{person}{Shane McIntosh}, {and} \bibinfo{person}{Martin Pinzger}.} \bibinfo{year}{2016}\natexlab{}.
\newblock \showarticletitle{Predicting {Build} {Co}-changes with {Source} {Code} {Change} and {Commit} {Categories}}. In \bibinfo{booktitle}{\emph{2016 {IEEE} 23rd {International} {Conference} on {Software} {Analysis}, {Evolution}, and {Reengineering} ({SANER})}}, Vol.~\bibinfo{volume}{1}. \bibinfo{pages}{541--551}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/SANER.2016.22}
\showDOI{\tempurl}


\bibitem[Macho et~al\mbox{.}(2018)]%
        {macho_automatically_2018}
\bibfield{author}{\bibinfo{person}{Christian Macho}, \bibinfo{person}{Shane McIntosh}, {and} \bibinfo{person}{Martin Pinzger}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Automatically repairing dependency-related build breakage}. In \bibinfo{booktitle}{\emph{2018 {IEEE} 25th {International} {Conference} on {Software} {Analysis}, {Evolution} and {Reengineering} ({SANER})}}. \bibinfo{pages}{106--117}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/SANER.2018.8330201}
\showDOI{\tempurl}


\bibitem[Maes-Bermejo et~al\mbox{.}(2022)]%
        {maes-bermejo_revisiting_2022}
\bibfield{author}{\bibinfo{person}{Michel Maes-Bermejo}, \bibinfo{person}{Micael Gallego}, \bibinfo{person}{Francisco Gortazar}, \bibinfo{person}{Gregorio Robles}, {and} \bibinfo{person}{Jesus~M. Gonzalez-Barahona}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Revisiting the building of past snapshots a replication and reproduction study}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{27}, \bibinfo{number}{3} (\bibinfo{year}{2022}).
\newblock
\showISSN{13823256}
\urldef\tempurl%
\url{http://dx.doi.org/10.1007/s10664-022-10117-6}
\showURL{%
\tempurl}


\bibitem[Malhotra(2015)]%
        {malhotra_systematic_2015}
\bibfield{author}{\bibinfo{person}{Ruchika Malhotra}.} \bibinfo{year}{2015}\natexlab{}.
\newblock \showarticletitle{A systematic review of machine learning techniques for software fault prediction}.
\newblock \bibinfo{journal}{\emph{Applied Soft Computing}}  \bibinfo{volume}{27} (\bibinfo{date}{Feb.} \bibinfo{year}{2015}), \bibinfo{pages}{504--518}.
\newblock
\showISSN{1568-4946}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.asoc.2014.11.023}
\showDOI{\tempurl}


\bibitem[Mcintosh et~al\mbox{.}(2014)]%
        {mcintosh_mining_2014}
\bibfield{author}{\bibinfo{person}{Shane Mcintosh}, \bibinfo{person}{Bram Adams}, \bibinfo{person}{Meiyappan Nagappan}, {and} \bibinfo{person}{Ahmed~E. Hassan}.} \bibinfo{year}{2014}\natexlab{}.
\newblock \showarticletitle{Mining {Co}-change {Information} to {Understand} {When} {Build} {Changes} {Are} {Necessary}}. In \bibinfo{booktitle}{\emph{2014 {IEEE} {International} {Conference} on {Software} {Maintenance} and {Evolution}}}. \bibinfo{pages}{241--250}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSME.2014.46}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 1063-6773}.


\bibitem[McIntosh et~al\mbox{.}(2011)]%
        {mcintosh_empirical_2011}
\bibfield{author}{\bibinfo{person}{Shane McIntosh}, \bibinfo{person}{Bram Adams}, \bibinfo{person}{Thanh~H.D. Nguyen}, \bibinfo{person}{Yasutaka Kamei}, {and} \bibinfo{person}{Ahmed~E. Hassan}.} \bibinfo{year}{2011}\natexlab{}.
\newblock \showarticletitle{An empirical study of build maintenance effort}. In \bibinfo{booktitle}{\emph{2011 33rd {International} {Conference} on {Software} {Engineering} ({ICSE})}}. \bibinfo{pages}{141--150}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1145/1985793.1985813}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 1558-1225}.


\bibitem[Memon et~al\mbox{.}(2017)]%
        {memon_taming_2017}
\bibfield{author}{\bibinfo{person}{Atif Memon}, \bibinfo{person}{Zebao Gao}, \bibinfo{person}{Bao Nguyen}, \bibinfo{person}{Sanjeev Dhanda}, \bibinfo{person}{Eric Nickell}, \bibinfo{person}{Rob Siemborski}, {and} \bibinfo{person}{John Micco}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Taming {Google}-scale continuous testing}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 39th {International} {Conference} on {Software} {Engineering}: {Software} {Engineering} in {Practice} {Track} ({ICSE}-{SEIP})}}. \bibinfo{pages}{233--242}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSE-SEIP.2017.16}
\showDOI{\tempurl}


\bibitem[Mesbah et~al\mbox{.}(2019)]%
        {mesbah_deepdelta_2019}
\bibfield{author}{\bibinfo{person}{Ali Mesbah}, \bibinfo{person}{Andrew Rice}, \bibinfo{person}{Emily Johnston}, \bibinfo{person}{Nick Glorioso}, {and} \bibinfo{person}{Edward Aftandilian}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{{DeepDelta}: {Learning} to repair compilation errors}. In \bibinfo{booktitle}{\emph{{ESEC}/{FSE} 2019 - {Proceedings} of the 2019 27th {ACM} {Joint} {Meeting} {European} {Software} {Engineering} {Conference} and {Symposium} on the {Foundations} of {Software} {Engineering}}}. \bibinfo{address}{Tallinn, Estonia}, \bibinfo{pages}{925 -- 936}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3338906.3340455}
\showURL{%
\tempurl}


\bibitem[Moriconi et~al\mbox{.}(2022)]%
        {moriconi_automated_2022}
\bibfield{author}{\bibinfo{person}{Florent Moriconi}, \bibinfo{person}{Rapahël Troncy}, \bibinfo{person}{Aurélien Francillon}, {and} \bibinfo{person}{Jihane Zouaoui}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Automated identification of flaky builds using knowledge graphs}.
\newblock
\urldef\tempurl%
\url{https://www.eurecom.fr/en/publication/7046}
\showURL{%
\tempurl}


\bibitem[Ni and Li(2017)]%
        {ni_cost-effective_2017}
\bibfield{author}{\bibinfo{person}{Ansong Ni} {and} \bibinfo{person}{Ming Li}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Cost-effective build outcome prediction using cascaded classifiers}. In \bibinfo{booktitle}{\emph{Proceedings of the 14th {International} {Conference} on {Mining} {Software} {Repositories}}} \emph{(\bibinfo{series}{{MSR} '17})}. \bibinfo{publisher}{IEEE Press}, \bibinfo{address}{Buenos Aires, Argentina}, \bibinfo{pages}{455--458}.
\newblock
\showISBNx{978-1-5386-1544-7}
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.26}
\showDOI{\tempurl}


\bibitem[Ni and Li(2018)]%
        {ni_acona_2018}
\bibfield{author}{\bibinfo{person}{Ansong Ni} {and} \bibinfo{person}{Ming Li}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{{ACONA}: {Active} online model adaptation for predicting continuous integration build failures}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Gothenburg, Sweden}, \bibinfo{pages}{366 -- 367}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3183440.3195012}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Olewicki et~al\mbox{.}(2022)]%
        {olewicki_towards_2022}
\bibfield{author}{\bibinfo{person}{Doriane Olewicki}, \bibinfo{person}{Mathieu Nayrolles}, {and} \bibinfo{person}{Bram Adams}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Towards language-independent {Brown} {Build} {Detection}}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}, Vol.~\bibinfo{volume}{2022-May}. \bibinfo{address}{Pittsburgh, PA, United states}, \bibinfo{pages}{2177 -- 2188}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3510003.3510122}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Pan et~al\mbox{.}(2021)]%
        {pan_test_2021}
\bibfield{author}{\bibinfo{person}{Rongqi Pan}, \bibinfo{person}{Mojtaba Bagherzadeh}, \bibinfo{person}{Taher~A. Ghaleb}, {and} \bibinfo{person}{Lionel Briand}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Test case selection and prioritization using machine learning: a systematic literature review}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{27}, \bibinfo{number}{2} (\bibinfo{date}{Dec.} \bibinfo{year}{2021}), \bibinfo{pages}{29}.
\newblock
\showISSN{1573-7616}
\urldef\tempurl%
\url{https://doi.org/10.1007/s10664-021-10066-6}
\showDOI{\tempurl}


\bibitem[Pinto et~al\mbox{.}(2017)]%
        {pinto_inadequate_2017}
\bibfield{author}{\bibinfo{person}{G. Pinto}, \bibinfo{person}{M. Reboucas}, {and} \bibinfo{person}{F. Castor}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Inadequate testing, time pressure, and (over) confidence: a tale of continuous integration users}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 10th {International} {Workshop} on {Cooperative} and {Human} {Aspects} of {Software} {Engineering} ({CHASE})}}. \bibinfo{address}{Los Alamitos, CA, USA}, \bibinfo{pages}{74 -- 7}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/CHASE.2017.13}
\showURL{%
\tempurl}


\bibitem[Rabbani et~al\mbox{.}(2018)]%
        {rabbani_revisiting_2018}
\bibfield{author}{\bibinfo{person}{Noam Rabbani}, \bibinfo{person}{Michael~S. Harvey}, \bibinfo{person}{Sadnan Saquif}, \bibinfo{person}{Keheliya Gallaba}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Revisiting "programmers' build errors" in the visual studio context: {A} replication study using {IDE} interaction traces}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Gothenburg, Sweden}, \bibinfo{pages}{98 -- 101}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3196398.3196469}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Randrianaina et~al\mbox{.}(2022)]%
        {randrianaina_towards_2022}
\bibfield{author}{\bibinfo{person}{Georges~Aaron Randrianaina}, \bibinfo{person}{Djamel~Eddine Khelladi}, \bibinfo{person}{Olivier Zendra}, {and} \bibinfo{person}{Mathieu Acher}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Towards incremental build of software configurations}. In \bibinfo{booktitle}{\emph{Proceedings of the {ACM}/{IEEE} 44th {International} {Conference} on {Software} {Engineering}: {New} {Ideas} and {Emerging} {Results}}} \emph{(\bibinfo{series}{{ICSE}-{NIER} '22})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{101--105}.
\newblock
\showISBNx{978-1-4503-9224-2}
\urldef\tempurl%
\url{https://doi.org/10.1145/3510455.3512792}
\showDOI{\tempurl}


\bibitem[Rausch et~al\mbox{.}(2017)]%
        {rausch_empirical_2017}
\bibfield{author}{\bibinfo{person}{Thomas Rausch}, \bibinfo{person}{Waldemar Hummer}, \bibinfo{person}{Philipp Leitner}, {and} \bibinfo{person}{Stefan Schulte}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{An {Empirical} {Analysis} of {Build} {Failures} in the {Continuous} {Integration} {Workflows} of {Java}-{Based} {Open}-{Source} {Software}}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 14th {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{345--355}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.54}
\showDOI{\tempurl}


\bibitem[Saidani and Ouni(2021)]%
        {saidani_toward_2021}
\bibfield{author}{\bibinfo{person}{Islem Saidani} {and} \bibinfo{person}{Ali Ouni}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Toward a {Smell}-aware {Prediction} {Model} for {CI} {Build} {Failures}}. In \bibinfo{booktitle}{\emph{Proceedings - 2021 36th {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} {Workshops}, {ASEW} 2021}}. \bibinfo{address}{Virtual, Online, Australia}, \bibinfo{pages}{18 -- 25}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/ASEW52652.2021.00017}
\showURL{%
\tempurl}


\bibitem[Saidani et~al\mbox{.}(2021b)]%
        {saidani_bf-detector_2021}
\bibfield{author}{\bibinfo{person}{I. Saidani}, \bibinfo{person}{A. Ouni}, \bibinfo{person}{M. Chouchen}, {and} \bibinfo{person}{M.W. Mkaouer}.} \bibinfo{year}{2021}\natexlab{b}.
\newblock \showarticletitle{{BF}-detector: an automated tool for {CI} build failure detection}. In \bibinfo{booktitle}{\emph{{ESEC}/{FSE} 2021: {Proceedings} of the 29th {ACM} {Joint} {Meeting} on {European} {Software} {Engineering} {Conference} and {Symposium} on the {Foundations} of {Software} {Engineering}}}. \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1530 -- 4}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3468264.3473115}
\showURL{%
\tempurl}


\bibitem[Saidani et~al\mbox{.}(2020a)]%
        {saidani_prediction_2020}
\bibfield{author}{\bibinfo{person}{Islem Saidani}, \bibinfo{person}{Ali Ouni}, \bibinfo{person}{Moataz Chouchen}, {and} \bibinfo{person}{Mohamed~Wiem Mkaouer}.} \bibinfo{year}{2020}\natexlab{a}.
\newblock \showarticletitle{On the prediction of continuous integration build failures using search-based software engineering}. In \bibinfo{booktitle}{\emph{Proceedings of the 2020 {Genetic} and {Evolutionary} {Computation} {Conference} {Companion}}} \emph{(\bibinfo{series}{{GECCO} '20})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{313--314}.
\newblock
\showISBNx{978-1-4503-7127-8}
\urldef\tempurl%
\url{https://doi.org/10.1145/3377929.3390050}
\showDOI{\tempurl}


\bibitem[Saidani et~al\mbox{.}(2020b)]%
        {saidani_predicting_2020}
\bibfield{author}{\bibinfo{person}{Islem Saidani}, \bibinfo{person}{Ali Ouni}, \bibinfo{person}{Moataz Chouchen}, {and} \bibinfo{person}{Mohamed~Wiem Mkaouer}.} \bibinfo{year}{2020}\natexlab{b}.
\newblock \showarticletitle{Predicting continuous integration build failures using evolutionary search}.
\newblock \bibinfo{journal}{\emph{Information and Software Technology}}  \bibinfo{volume}{128} (\bibinfo{date}{Dec.} \bibinfo{year}{2020}), \bibinfo{pages}{106392}.
\newblock
\showISSN{0950-5849}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.infsof.2020.106392}
\showDOI{\tempurl}


\bibitem[Saidani et~al\mbox{.}(2022)]%
        {saidani_improving_2022}
\bibfield{author}{\bibinfo{person}{Islem Saidani}, \bibinfo{person}{Ali Ouni}, {and} \bibinfo{person}{Mohamed~Wiem Mkaouer}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Improving the prediction of continuous integration build failures using deep learning}.
\newblock \bibinfo{journal}{\emph{Automated Software Engineering}} \bibinfo{volume}{29}, \bibinfo{number}{1} (\bibinfo{year}{2022}).
\newblock
\showISSN{09288910}
\urldef\tempurl%
\url{http://dx.doi.org/10.1007/s10515-021-00319-5}
\showURL{%
\tempurl}


\bibitem[Saidani et~al\mbox{.}(2021a)]%
        {saidani_detecting_2021}
\bibfield{author}{\bibinfo{person}{Islem Saidani}, \bibinfo{person}{Ali Ouni}, {and} \bibinfo{person}{Wiem Mkaouer}.} \bibinfo{year}{2021}\natexlab{a}.
\newblock \showarticletitle{Detecting {Skipped} {Commits} in {Continuous} {Integration} {Using} {Multi}-objective {Evolutionary} {Search}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} (\bibinfo{year}{2021}).
\newblock
\showISSN{00985589}
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/TSE.2021.3129165}
\showURL{%
\tempurl}


\bibitem[Santolucito et~al\mbox{.}(2022)]%
        {santolucito_learning_2022}
\bibfield{author}{\bibinfo{person}{Mark Santolucito}, \bibinfo{person}{Jialu Zhang}, \bibinfo{person}{Ennan Zhai}, \bibinfo{person}{Jürgen Cito}, {and} \bibinfo{person}{Ruzica Piskac}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{Learning {CI} {Configuration} {Correctness} for {Early} {Build} {Feedback}}. In \bibinfo{booktitle}{\emph{2022 {IEEE} {International} {Conference} on {Software} {Analysis}, {Evolution} and {Reengineering} ({SANER})}}. \bibinfo{pages}{1006--1017}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/SANER53432.2022.00118}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 1534-5351}.


\bibitem[Seo et~al\mbox{.}(2014)]%
        {seo_programmers_2014}
\bibfield{author}{\bibinfo{person}{Hyunmin Seo}, \bibinfo{person}{Caitlin Sadowski}, \bibinfo{person}{Sebastian Elbaum}, \bibinfo{person}{Edward Aftandilian}, {and} \bibinfo{person}{Robert Bowdidge}.} \bibinfo{year}{2014}\natexlab{}.
\newblock \showarticletitle{Programmers' build errors: {A} case study (at google)}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}, Vol.~\bibinfo{volume}{0}. \bibinfo{address}{Hyderabad, India}, \bibinfo{pages}{724 -- 734}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/2568225.2568255}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257 Issue: 1}.


\bibitem[Shahin et~al\mbox{.}(2017)]%
        {shahin_continuous_2017}
\bibfield{author}{\bibinfo{person}{Mojtaba Shahin}, \bibinfo{person}{Muhammad Ali~Babar}, {and} \bibinfo{person}{Liming Zhu}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Continuous {Integration}, {Delivery} and {Deployment}: {A} {Systematic} {Review} on {Approaches}, {Tools}, {Challenges} and {Practices}}.
\newblock \bibinfo{journal}{\emph{IEEE Access}}  \bibinfo{volume}{5} (\bibinfo{year}{2017}), \bibinfo{pages}{3909--3943}.
\newblock
\showISSN{2169-3536}
\urldef\tempurl%
\url{https://doi.org/10.1109/ACCESS.2017.2685629}
\showDOI{\tempurl}


\bibitem[Shimmi and Rahimi(2024)]%
        {shimmi_association_2024}
\bibfield{author}{\bibinfo{person}{Samiha Shimmi} {and} \bibinfo{person}{Mona Rahimi}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{On {Association} of {Code} {Change} {Types} and {CI} {Build} {Failures} in {Software} {Repositories}}.
\newblock \bibinfo{journal}{\emph{European Journal of Information Technologies and Computer Science}} \bibinfo{volume}{4}, \bibinfo{number}{2} (\bibinfo{date}{June} \bibinfo{year}{2024}), \bibinfo{pages}{1--15}.
\newblock
\showISSN{2736-5492}
\urldef\tempurl%
\url{https://doi.org/10.24018/compute.2024.4.2.124}
\showDOI{\tempurl}
\newblock
\shownote{Number: 2}.


\bibitem[Silva et~al\mbox{.}(2023)]%
        {silva_what_2023}
\bibfield{author}{\bibinfo{person}{Gustavo Silva}, \bibinfo{person}{Carla Bezerra}, \bibinfo{person}{Anderson Uchôa}, {and} \bibinfo{person}{Ivan Machado}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{What {Factors} {Affect} the {Build} {Failures} {Correction} {Time}? {A} {Multi}-{Project} {Study}}. In \bibinfo{booktitle}{\emph{Proceedings of the 17th {Brazilian} {Symposium} on {Software} {Components}, {Architectures}, and {Reuse}}} \emph{(\bibinfo{series}{{SBCARS} '23})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{41--50}.
\newblock
\showISBNx{9798400709524}
\urldef\tempurl%
\url{https://doi.org/10.1145/3622748.3622753}
\showDOI{\tempurl}


\bibitem[Ståhl and Bosch(2014)]%
        {stahl_modeling_2014}
\bibfield{author}{\bibinfo{person}{Daniel Ståhl} {and} \bibinfo{person}{Jan Bosch}.} \bibinfo{year}{2014}\natexlab{}.
\newblock \showarticletitle{Modeling continuous integration practice differences in industry software development}.
\newblock \bibinfo{journal}{\emph{Journal of Systems and Software}}  \bibinfo{volume}{87} (\bibinfo{date}{Jan.} \bibinfo{year}{2014}), \bibinfo{pages}{48--59}.
\newblock
\showISSN{0164-1212}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.jss.2013.08.032}
\showDOI{\tempurl}


\bibitem[Sulír et~al\mbox{.}(2020)]%
        {sulir_large-scale_2020}
\bibfield{author}{\bibinfo{person}{Matúš Sulír}, \bibinfo{person}{Michaela Bačíková}, \bibinfo{person}{Matej Madeja}, \bibinfo{person}{Sergej Chodarev}, {and} \bibinfo{person}{Ján Juhár}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Large-{Scale} {Dataset} of {Local} {Java} {Software} {Build} {Results}}.
\newblock \bibinfo{journal}{\emph{Data}} \bibinfo{volume}{5}, \bibinfo{number}{3} (\bibinfo{date}{Sept.} \bibinfo{year}{2020}), \bibinfo{pages}{86}.
\newblock
\showISSN{2306-5729}
\urldef\tempurl%
\url{https://doi.org/10.3390/data5030086}
\showDOI{\tempurl}
\newblock
\shownote{Number: 3 Publisher: Multidisciplinary Digital Publishing Institute}.


\bibitem[Sun et~al\mbox{.}(2024)]%
        {sun_ravenbuild_2024}
\bibfield{author}{\bibinfo{person}{Gengyi Sun}, \bibinfo{person}{Sarra Habchi}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{{RavenBuild}: {Context}, {Relevance}, and {Dependency} {Aware} {Build} {Outcome} {Prediction}}.
\newblock \bibinfo{journal}{\emph{RavenBuild: Context, Relevance, and Dependency Aware Build Outcome Prediction}} \bibinfo{volume}{1}, \bibinfo{number}{FSE} (\bibinfo{date}{July} \bibinfo{year}{2024}), \bibinfo{pages}{45:996--45:1018}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1145/3643771}
\showDOI{\tempurl}


\bibitem[Tufano et~al\mbox{.}(2019)]%
        {tufano_towards_2019}
\bibfield{author}{\bibinfo{person}{Michele Tufano}, \bibinfo{person}{Hitesh Sajnani}, {and} \bibinfo{person}{Kim Herzig}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{Towards {Predicting} the {Impact} of {Software} {Changes} on {Building} {Activities}}. In \bibinfo{booktitle}{\emph{2019 {IEEE}/{ACM} 41st {International} {Conference} on {Software} {Engineering}: {New} {Ideas} and {Emerging} {Results} ({ICSE}-{NIER})}}. \bibinfo{pages}{49--52}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSE-NIER.2019.00021}
\showDOI{\tempurl}


\bibitem[Vassallo(2019)]%
        {vassallo_enabling_2019}
\bibfield{author}{\bibinfo{person}{C. Vassallo}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{Enabling {Continuous} {Improvement} of a {Continuous} {Integration} {Process}}. In \bibinfo{booktitle}{\emph{2019 34th {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering} ({ASE}). {Proceedings}}}. \bibinfo{address}{Los Alamitos, CA, USA}, \bibinfo{pages}{1246 -- 9}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1109/ASE.2019.00151}
\showURL{%
\tempurl}


\bibitem[Vassallo et~al\mbox{.}(2020)]%
        {vassallo_every_2020}
\bibfield{author}{\bibinfo{person}{C. Vassallo}, \bibinfo{person}{S. Proksch}, \bibinfo{person}{T. Zemp}, {and} \bibinfo{person}{H.C. Gall}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Every build you break: developer-oriented assistance for build failure resolution}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{25}, \bibinfo{number}{3} (\bibinfo{year}{2020}), \bibinfo{pages}{2218 -- 57}.
\newblock
\showISSN{1382-3256}
\urldef\tempurl%
\url{http://dx.doi.org/10.1007/s10664-019-09765-y}
\showURL{%
\tempurl}
\newblock
\shownote{Place: Germany}.


\bibitem[Vassallo et~al\mbox{.}(2018)]%
        {vassallo_-break_2018}
\bibfield{author}{\bibinfo{person}{Carmine Vassallo}, \bibinfo{person}{Sebastian Proksch}, \bibinfo{person}{Timothy Zemp}, {and} \bibinfo{person}{Harald~C. Gall}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Un-break my build: {Assisting} developers with build repair hints}. In \bibinfo{booktitle}{\emph{Proceedings - {International} {Conference} on {Software} {Engineering}}}. \bibinfo{address}{Gothenburg, Sweden}, \bibinfo{pages}{41 -- 51}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3196321.3196350}
\showURL{%
\tempurl}
\newblock
\shownote{ISSN: 02705257}.


\bibitem[Vassallo et~al\mbox{.}(2017)]%
        {vassallo_tale_2017}
\bibfield{author}{\bibinfo{person}{Carmine Vassallo}, \bibinfo{person}{Gerald Schermann}, \bibinfo{person}{Fiorella Zampetti}, \bibinfo{person}{Daniele Romano}, \bibinfo{person}{Philipp Leitner}, \bibinfo{person}{Andy Zaidman}, \bibinfo{person}{Massimiliano Di~Penta}, {and} \bibinfo{person}{Sebastiano Panichella}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{A {Tale} of {CI} {Build} {Failures}: {An} {Open} {Source} and a {Financial} {Organization} {Perspective}}. In \bibinfo{booktitle}{\emph{2017 {IEEE} {International} {Conference} on {Software} {Maintenance} and {Evolution} ({ICSME})}}. \bibinfo{pages}{183--193}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSME.2017.67}
\showDOI{\tempurl}


\bibitem[Wang et~al\mbox{.}(2024)]%
        {wang_commit_2024}
\bibfield{author}{\bibinfo{person}{Guoqing Wang}, \bibinfo{person}{Zeyu Sun}, \bibinfo{person}{Yizhou Chen}, \bibinfo{person}{Yifan Zhao}, \bibinfo{person}{Qingyuan Liang}, {and} \bibinfo{person}{Dan Hao}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{Commit {Artifact} {Preserving} {Build} {Prediction}}. In \bibinfo{booktitle}{\emph{Proceedings of the 33rd {ACM} {SIGSOFT} {International} {Symposium} on {Software} {Testing} and {Analysis}}} \emph{(\bibinfo{series}{{ISSTA} 2024})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1236--1248}.
\newblock
\showISBNx{9798400706127}
\urldef\tempurl%
\url{https://doi.org/10.1145/3650212.3680356}
\showDOI{\tempurl}


\bibitem[Warfel and Donna(2004)]%
        {warfel_card_2004}
\bibfield{author}{\bibinfo{person}{Todd Warfel} {and} \bibinfo{person}{Spencer Donna}.} \bibinfo{year}{2004}\natexlab{}.
\newblock \bibinfo{title}{Card {Sorting}: {A} {Definitive} {Guide}}.
\newblock
\newblock
\urldef\tempurl%
\url{https://boxesandarrows.com/card-sorting-a-definitive-guide/}
\showURL{%
\tempurl}


\bibitem[Weeraddana et~al\mbox{.}(2024a)]%
        {weeraddana_characterizing_2024}
\bibfield{author}{\bibinfo{person}{Nimmi Weeraddana}, \bibinfo{person}{Mahmoud Alfadel}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2024}\natexlab{a}.
\newblock \showarticletitle{Characterizing {Timeout} {Builds} in {Continuous} {Integration}}.
\newblock \bibinfo{journal}{\emph{IEEE Transactions on Software Engineering}} \bibinfo{volume}{50}, \bibinfo{number}{6} (\bibinfo{date}{June} \bibinfo{year}{2024}), \bibinfo{pages}{1450--1463}.
\newblock
\showISSN{1939-3520}
\urldef\tempurl%
\url{https://doi.org/10.1109/TSE.2024.3387840}
\showDOI{\tempurl}
\newblock
\shownote{Conference Name: IEEE Transactions on Software Engineering}.


\bibitem[Weeraddana et~al\mbox{.}(2024b)]%
        {weeraddana_dependency-induced_2024}
\bibfield{author}{\bibinfo{person}{Nimmi~Rashinika Weeraddana}, \bibinfo{person}{Mahmoud Alfadel}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2024}\natexlab{b}.
\newblock \showarticletitle{Dependency-{Induced} {Waste} in {Continuous} {Integration}: {An} {Empirical} {Study} of {Unused} {Dependencies} in the npm {Ecosystem}}.
\newblock \bibinfo{journal}{\emph{Proc. ACM Softw. Eng.}} \bibinfo{volume}{1}, \bibinfo{number}{FSE} (\bibinfo{date}{July} \bibinfo{year}{2024}), \bibinfo{pages}{116:2632--116:2655}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1145/3660823}
\showDOI{\tempurl}


\bibitem[Widder et~al\mbox{.}(2018)]%
        {widder_im_2018}
\bibfield{author}{\bibinfo{person}{David Widder}, \bibinfo{person}{Bogdan Vasilescu}, \bibinfo{person}{Michael Hilton}, {and} \bibinfo{person}{Christian Kästner}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{I'm {Leaving} {You}, {Travis}: {A} {Continuous} {Integration} {Breakup} {Story}}. In \bibinfo{booktitle}{\emph{2018 {IEEE}/{ACM} 15th {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{165--169}.
\newblock
\newblock
\shownote{ISSN: 2574-3864}.


\bibitem[Wohlin et~al\mbox{.}(2020)]%
        {wohlin_guidelines_2020}
\bibfield{author}{\bibinfo{person}{Claes Wohlin}, \bibinfo{person}{Emilia Mendes}, \bibinfo{person}{Katia~Romero Felizardo}, {and} \bibinfo{person}{Marcos Kalinowski}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Guidelines for the search strategy to update systematic literature reviews in software engineering}.
\newblock \bibinfo{journal}{\emph{Information and Software Technology}}  \bibinfo{volume}{127} (\bibinfo{date}{Nov.} \bibinfo{year}{2020}), \bibinfo{pages}{106366}.
\newblock
\showISSN{0950-5849}
\urldef\tempurl%
\url{https://doi.org/10.1016/j.infsof.2020.106366}
\showDOI{\tempurl}


\bibitem[Wolf et~al\mbox{.}(2009)]%
        {wolf_predicting_2009}
\bibfield{author}{\bibinfo{person}{Timo Wolf}, \bibinfo{person}{Adrian Schroter}, \bibinfo{person}{Daniela Damian}, {and} \bibinfo{person}{Thanh Nguyen}.} \bibinfo{year}{2009}\natexlab{}.
\newblock \showarticletitle{Predicting build failures using social network analysis on developer communication}. In \bibinfo{booktitle}{\emph{Proceedings of the 31st {International} {Conference} on {Software} {Engineering}}} \emph{(\bibinfo{series}{{ICSE} '09})}. \bibinfo{publisher}{IEEE Computer Society}, \bibinfo{address}{USA}, \bibinfo{pages}{1--11}.
\newblock
\showISBNx{978-1-4244-3453-4}
\urldef\tempurl%
\url{https://doi.org/10.1109/ICSE.2009.5070503}
\showDOI{\tempurl}


\bibitem[Wróbel et~al\mbox{.}(2023)]%
        {wrobel_using_2023}
\bibfield{author}{\bibinfo{person}{Michal~R. Wróbel}, \bibinfo{person}{Jaroslaw Szymukowicz}, {and} \bibinfo{person}{Pawel Weichbroth}.} \bibinfo{year}{2023}\natexlab{}.
\newblock \showarticletitle{Using {Continuous} {Integration} {Techniques} in {Open} {Source} {Projects}—{An} {Exploratory} {Study}}.
\newblock \bibinfo{journal}{\emph{IEEE Access}}  \bibinfo{volume}{11} (\bibinfo{year}{2023}), \bibinfo{pages}{113848--113863}.
\newblock
\showISSN{2169-3536}
\urldef\tempurl%
\url{https://doi.org/10.1109/ACCESS.2023.3324536}
\showDOI{\tempurl}
\newblock
\shownote{Conference Name: IEEE Access}.


\bibitem[Wu et~al\mbox{.}(2020a)]%
        {wu_using_2020}
\bibfield{author}{\bibinfo{person}{Yiwen Wu}, \bibinfo{person}{Yang Zhang}, \bibinfo{person}{Junsheng Chang}, \bibinfo{person}{Bo Ding}, \bibinfo{person}{Tao Wang}, {and} \bibinfo{person}{Huaimin Wang}.} \bibinfo{year}{2020}\natexlab{a}.
\newblock \showarticletitle{Using {Configuration} {Semantic} {Features} and {Machine} {Learning} {Algorithms} to {Predict} {Build} {Result} in {Cloud}-{Based} {Container} {Environment}}. In \bibinfo{booktitle}{\emph{2020 {IEEE} 26th {International} {Conference} on {Parallel} and {Distributed} {Systems} ({ICPADS})}}. \bibinfo{pages}{248--255}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/ICPADS51040.2020.00042}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 2690-5965}.


\bibitem[Wu et~al\mbox{.}(2020b)]%
        {wu_empirical_2020}
\bibfield{author}{\bibinfo{person}{Yiwen Wu}, \bibinfo{person}{Yang Zhang}, \bibinfo{person}{Tao Wang}, {and} \bibinfo{person}{Huaimin Wang}.} \bibinfo{year}{2020}\natexlab{b}.
\newblock \showarticletitle{An {Empirical} {Study} of {Build} {Failures} in the {Docker} {Context}}. In \bibinfo{booktitle}{\emph{Proceedings of the 17th {International} {Conference} on {Mining} {Software} {Repositories}}} \emph{(\bibinfo{series}{{MSR} '20})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{76--80}.
\newblock
\showISBNx{978-1-4503-7517-7}
\urldef\tempurl%
\url{https://doi.org/10.1145/3379597.3387483}
\showDOI{\tempurl}


\bibitem[Xia and Li(2017)]%
        {xia_could_2017}
\bibfield{author}{\bibinfo{person}{Jing Xia} {and} \bibinfo{person}{Yanhui Li}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Could {We} {Predict} the {Result} of a {Continuous} {Integration} {Build}? {An} {Empirical} {Study}}. In \bibinfo{booktitle}{\emph{2017 {IEEE} {International} {Conference} on {Software} {Quality}, {Reliability} and {Security} {Companion} ({QRS}-{C})}}. \bibinfo{pages}{311--315}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/QRS-C.2017.59}
\showDOI{\tempurl}


\bibitem[Xia et~al\mbox{.}(2017)]%
        {xia_empirical_2017}
\bibfield{author}{\bibinfo{person}{Jing Xia}, \bibinfo{person}{Yanhui Li}, {and} \bibinfo{person}{Chuanqi Wang}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{An {Empirical} {Study} on the {Cross}-{Project} {Predictability} of {Continuous} {Integration} {Outcomes}}. In \bibinfo{booktitle}{\emph{2017 14th {Web} {Information} {Systems} and {Applications} {Conference} ({WISA})}}. \bibinfo{pages}{234--239}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/WISA.2017.53}
\showDOI{\tempurl}


\bibitem[Xia et~al\mbox{.}(2015)]%
        {xia_cross-project_2015}
\bibfield{author}{\bibinfo{person}{Xin Xia}, \bibinfo{person}{David Lo}, \bibinfo{person}{Shane McIntosh}, \bibinfo{person}{Emad Shihab}, {and} \bibinfo{person}{Ahmed~E. Hassan}.} \bibinfo{year}{2015}\natexlab{}.
\newblock \showarticletitle{Cross-project build co-change prediction}. In \bibinfo{booktitle}{\emph{2015 {IEEE} 22nd {International} {Conference} on {Software} {Analysis}, {Evolution}, and {Reengineering} ({SANER})}}. \bibinfo{pages}{311--320}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/SANER.2015.7081841}
\showDOI{\tempurl}
\newblock
\shownote{ISSN: 1534-5351}.


\bibitem[Xie and Li(2018)]%
        {xie_cutting_2018}
\bibfield{author}{\bibinfo{person}{Zheng Xie} {and} \bibinfo{person}{Ming Li}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Cutting the {Software} {Building} {Efforts} in {Continuous} {Integration} by {Semi}-{Supervised} {Online} {AUC} {Optimization}}. In \bibinfo{booktitle}{\emph{Proceedings of the {Twenty}-{Seventh} {International} {Joint} {Conference} on {Artificial} {Intelligence}}}. \bibinfo{publisher}{International Joint Conferences on Artificial Intelligence Organization}, \bibinfo{address}{Stockholm, Sweden}, \bibinfo{pages}{2875--2881}.
\newblock
\showISBNx{978-0-9992411-2-7}
\urldef\tempurl%
\url{https://doi.org/10.24963/ijcai.2018/399}
\showDOI{\tempurl}


\bibitem[Yin et~al\mbox{.}(2024)]%
        {yin_developer-applied_2024}
\bibfield{author}{\bibinfo{person}{Mingyang Yin}, \bibinfo{person}{Yutaro Kashiwa}, \bibinfo{person}{Keheliya Gallaba}, \bibinfo{person}{Mahmoud Alfadel}, \bibinfo{person}{Yasutaka Kamei}, {and} \bibinfo{person}{Shane McIntosh}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \showarticletitle{Developer-{Applied} {Accelerations} in {Continuous} {Integration}: {A} {Detection} {Approach} and {Catalog} of {Patterns}}. In \bibinfo{booktitle}{\emph{Proceedings of the 39th {IEEE}/{ACM} {International} {Conference} on {Automated} {Software} {Engineering}}} \emph{(\bibinfo{series}{{ASE} '24})}. \bibinfo{publisher}{Association for Computing Machinery}, \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{1655--1666}.
\newblock
\showISBNx{9798400712487}
\urldef\tempurl%
\url{https://doi.org/10.1145/3691620.3695533}
\showDOI{\tempurl}


\bibitem[Zhang et~al\mbox{.}(2019)]%
        {zhang_large-scale_2019}
\bibfield{author}{\bibinfo{person}{Chen Zhang}, \bibinfo{person}{Bihuan Chen}, \bibinfo{person}{Linlin Chen}, \bibinfo{person}{Xin Peng}, {and} \bibinfo{person}{Wenyun Zhao}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{A large-scale empirical study of compiler errors in continuous integration}. In \bibinfo{booktitle}{\emph{{ESEC}/{FSE} 2019: {Proceedings} of the 2019 27th {ACM} {Joint} {Meeting} on {European} {Software} {Engineering} {Conference} and {Symposium} on the {Foundations} of {Software} {Engineering}}}. \bibinfo{address}{New York, NY, USA}, \bibinfo{pages}{176 -- 87}.
\newblock
\urldef\tempurl%
\url{http://dx.doi.org/10.1145/3338906.3338917}
\showURL{%
\tempurl}


\bibitem[Zhang et~al\mbox{.}(2022)]%
        {zhang_buildsonic_2022}
\bibfield{author}{\bibinfo{person}{Chen Zhang}, \bibinfo{person}{Bihuan Chen}, \bibinfo{person}{Junhao Hu}, \bibinfo{person}{Xin Peng}, {and} \bibinfo{person}{Wenyun Zhao}.} \bibinfo{year}{2022}\natexlab{}.
\newblock \showarticletitle{{BuildSonic}: {Detecting} and {Repairing} {Performance}-{Related} {Configuration} {Smells} for {Continuous} {Integration} {Builds}}.
\newblock  (\bibinfo{year}{2022}), \bibinfo{pages}{13}.
\newblock


\bibitem[Zolfagharinia et~al\mbox{.}(2019)]%
        {zolfagharinia_study_2019}
\bibfield{author}{\bibinfo{person}{M. Zolfagharinia}, \bibinfo{person}{B. Adams}, {and} \bibinfo{person}{Y.-G. Gueheneuc}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{A study of build inflation in 30 million {CPAN} builds on 13 {Perl} versions and 10 operating systems}.
\newblock \bibinfo{journal}{\emph{Empirical Software Engineering}} \bibinfo{volume}{24}, \bibinfo{number}{6} (\bibinfo{year}{2019}), \bibinfo{pages}{3933 -- 71}.
\newblock
\showISSN{1382-3256}
\urldef\tempurl%
\url{http://dx.doi.org/10.1007/s10664-019-09709-6}
\showURL{%
\tempurl}
\newblock
\shownote{Place: Germany}.


\bibitem[Zolfagharinia et~al\mbox{.}(2017)]%
        {zolfagharinia_not_2017}
\bibfield{author}{\bibinfo{person}{Mahdis Zolfagharinia}, \bibinfo{person}{Bram Adams}, {and} \bibinfo{person}{Yann-Gaël Guéhénuc}.} \bibinfo{year}{2017}\natexlab{}.
\newblock \showarticletitle{Do {Not} {Trust} {Build} {Results} at {Face} {Value} - {An} {Empirical} {Study} of 30 {Million} {CPAN} {Builds}}. In \bibinfo{booktitle}{\emph{2017 {IEEE}/{ACM} 14th {International} {Conference} on {Mining} {Software} {Repositories} ({MSR})}}. \bibinfo{pages}{312--322}.
\newblock
\urldef\tempurl%
\url{https://doi.org/10.1109/MSR.2017.7}
\showDOI{\tempurl}


\end{thebibliography}
