#!/usr/bin/env python3
"""
bblbib.py - Convert LaTeX .bbl bibliography files to BibTeX .bib format

This script reads a main.bbl file and converts it to BibTeX format,
outputting the result to a .bib file.

Author: Generated by Augment Agent
Date: 2025-09-19
"""

import re
from typing import List, Tuple
from pathlib import Path


class BBLToBibConverter:
    """Converter class for transforming .bbl files to .bib format."""

    def __init__(self) -> None:
        """Initialize the converter with common patterns and mappings."""
        # Common entry type mappings based on typical .bbl patterns
        self.entry_type_patterns = {
            r'\\newblock\s+.*journal.*': 'article',
            r'\\newblock\s+.*proceedings.*': 'inproceedings',
            r'\\newblock\s+.*conference.*': 'inproceedings',
            r'\\newblock\s+.*book.*': 'book',
            r'\\newblock\s+.*thesis.*': 'phdthesis',
            r'\\newblock\s+.*technical.*report.*': 'techreport',
            r'\\newblock\s+.*arxiv.*': 'misc',
            r'\\newblock\s+.*url.*': 'misc',
            r'\\newblock\s+.*www.*': 'misc',
        }

        # Field extraction patterns
        self.field_patterns = {
            'author': [
                r'([^.]+?)\.\s*\\newblock',
                r'^([^\\]+?)(?=\\newblock|$)',
            ],
            'title': [
                r'\\newblock\s+([^.]+?)\.\s*\\newblock',
                r'\\newblock\s+``([^\']+?)\'\'',
                r'\\newblock\s+"([^"]+?)"',
                r'\\newblock\s+([^.]+?)\.(?:\s*In\s+|\s*\\newblock)',
            ],
            'journal': [
                r'\\newblock\s+(?:In\s+)?\\emph\{([^}]+)\}',
                r'\\newblock\s+(?:In\s+)?([A-Z][^,.\n]*(?:Journal|Review|Magazine|Transactions)[^,.\n]*)',
            ],
            'booktitle': [
                r'\\newblock\s+In\s+([^,.\n]+(?:Conference|Proceedings|Workshop|Symposium)[^,.\n]*)',
                r'\\newblock\s+In\s+\\emph\{([^}]+)\}',
            ],
            'year': [
                r'(\d{4})',
            ],
            'pages': [
                r'pages?\s+(\d+(?:--\d+)?)',
                r'pp\.\s*(\d+(?:--\d+)?)',
                r'(\d+)--(\d+)',
            ],
            'volume': [
                r'volume\s+(\d+)',
                r'vol\.\s*(\d+)',
                r'(\d+)\(\d+\)',
            ],
            'number': [
                r'number\s+(\d+)',
                r'no\.\s*(\d+)',
                r'\d+\((\d+)\)',
            ],
            'publisher': [
                r'\\newblock\s+([A-Z][^,.\n]*(?:Press|Publisher|Publications)[^,.\n]*)',
            ],
            'doi': [
                r'doi:\s*([^\s,]+)',
                r'DOI:\s*([^\s,]+)',
            ],
            'url': [
                r'\\url\{([^}]+)\}',
                r'https?://[^\s,]+',
            ],
        }

    def read_bbl_file(self, filepath: str) -> str:
        """Read the .bbl file content."""
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                content = file.read()
            print(f"✓ Successfully read {filepath}")
            return content
        except FileNotFoundError:
            raise FileNotFoundError(f"File {filepath} not found")
        except Exception as e:
            raise Exception(f"Error reading {filepath}: {str(e)}")

    def extract_bibliography_items(self, content: str) -> List[str]:
        """Extract individual bibliography items from .bbl content."""
        # Remove comments and clean up
        content = re.sub(r'%.*$', '', content, flags=re.MULTILINE)

        # Find bibliography environment
        bib_match = re.search(r'\\begin\{thebibliography\}.*?\\end\{thebibliography\}',
                             content, re.DOTALL)
        if not bib_match:
            raise ValueError("No bibliography environment found in .bbl file")

        bib_content = bib_match.group(0)

        # Extract individual bibitem entries
        items = re.findall(r'\\bibitem(?:\[[^\]]*\])?\{([^}]+)\}(.*?)(?=\\bibitem|\\end\{thebibliography\})',
                          bib_content, re.DOTALL)

        if not items:
            raise ValueError("No bibliography items found")

        print(f"✓ Found {len(items)} bibliography items")
        return items

    def determine_entry_type(self, content: str) -> str:
        """Determine the BibTeX entry type based on content patterns."""
        content_lower = content.lower()

        # Check for specific patterns
        for pattern, entry_type in self.entry_type_patterns.items():
            if re.search(pattern, content_lower, re.IGNORECASE):
                return entry_type

        # Default fallback logic
        if any(word in content_lower for word in ['journal', 'vol.', 'volume']):
            return 'article'
        elif any(word in content_lower for word in ['proceedings', 'conference', 'workshop']):
            return 'inproceedings'
        elif any(word in content_lower for word in ['book', 'publisher']):
            return 'book'
        elif any(word in content_lower for word in ['thesis', 'dissertation']):
            return 'phdthesis'
        elif any(word in content_lower for word in ['technical', 'report']):
            return 'techreport'
        else:
            return 'misc'

    def extract_field(self, content: str, field_name: str) -> str:
        """Extract a specific field from bibliography content."""
        patterns = self.field_patterns.get(field_name, [])

        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                value = match.group(1).strip()
                # Clean up the value
                value = re.sub(r'\\[a-zA-Z]+\{([^}]*)\}', r'\1', value)  # Remove LaTeX commands
                value = re.sub(r'\\[a-zA-Z]+', '', value)  # Remove remaining LaTeX commands
                value = re.sub(r'\s+', ' ', value)  # Normalize whitespace
                value = value.strip('.,;')  # Remove trailing punctuation
                if value:
                    return value

        return ""

    def clean_author_field(self, author: str) -> str:
        """Clean and format author field."""
        if not author:
            return ""

        # Remove LaTeX formatting
        author = re.sub(r'\\[a-zA-Z]+\{([^}]*)\}', r'\1', author)
        author = re.sub(r'\\[a-zA-Z]+', '', author)

        # Handle multiple authors
        authors = re.split(r'\s+and\s+|\s*,\s*(?=[A-Z])', author)
        cleaned_authors = []

        for auth in authors:
            auth = auth.strip()
            if auth:
                # Convert "First Last" to "Last, First" if needed
                if ',' not in auth and ' ' in auth:
                    parts = auth.split()
                    if len(parts) >= 2:
                        auth = f"{parts[-1]}, {' '.join(parts[:-1])}"
                cleaned_authors.append(auth)

        return ' and '.join(cleaned_authors)

    def convert_item_to_bibtex(self, cite_key: str, content: str) -> str:
        """Convert a single bibliography item to BibTeX format."""
        entry_type = self.determine_entry_type(content)

        # Extract fields
        fields = {}
        for field_name in ['author', 'title', 'journal', 'booktitle', 'year',
                          'pages', 'volume', 'number', 'publisher', 'doi', 'url']:
            value = self.extract_field(content, field_name)
            if value:
                if field_name == 'author':
                    value = self.clean_author_field(value)
                fields[field_name] = value

        # Build BibTeX entry
        bibtex_lines = [f"@{entry_type}{{{cite_key},"]

        for field, value in fields.items():
            if value:
                # Escape special characters and wrap in braces
                value = value.replace('{', '\\{').replace('}', '\\}')
                bibtex_lines.append(f"  {field} = {{{value}}},")

        # Remove trailing comma from last field
        if len(bibtex_lines) > 1:
            bibtex_lines[-1] = bibtex_lines[-1].rstrip(',')

        bibtex_lines.append("}")

        return '\n'.join(bibtex_lines)

    def convert_bbl_to_bib(self, input_file: str, output_file: str = None) -> None:
        """Convert .bbl file to .bib format."""
        if output_file is None:
            output_file = input_file.replace('.bbl', '.bib')

        print(f"Converting {input_file} to {output_file}...")

        try:
            # Read input file
            content = self.read_bbl_file(input_file)

            # Extract bibliography items
            items = self.extract_bibliography_items(content)

            # Convert each item
            bibtex_entries = []
            successful_conversions = 0
            failed_conversions = 0

            for cite_key, item_content in items:
                try:
                    bibtex_entry = self.convert_item_to_bibtex(cite_key, item_content)
                    bibtex_entries.append(bibtex_entry)
                    successful_conversions += 1
                    print(f"  ✓ Converted: {cite_key}")
                except Exception as e:
                    print(f"  ✗ Failed to convert {cite_key}: {str(e)}")
                    failed_conversions += 1

            # Write output file
            if bibtex_entries:
                with open(output_file, 'w', encoding='utf-8') as file:
                    file.write("% BibTeX bibliography generated from .bbl file\n")
                    file.write(f"% Generated on: {self._get_timestamp()}\n")
                    file.write(f"% Source: {input_file}\n\n")
                    file.write('\n\n'.join(bibtex_entries))
                    file.write('\n')

                print(f"\n✓ Conversion completed successfully!")
                print(f"  Output file: {output_file}")
                print(f"  Successful conversions: {successful_conversions}")
                if failed_conversions > 0:
                    print(f"  Failed conversions: {failed_conversions}")
            else:
                raise Exception("No entries were successfully converted")

        except Exception as e:
            print(f"\n✗ Conversion failed: {str(e)}")
            raise

    def _get_timestamp(self) -> str:
        """Get current timestamp for file header."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main() -> int:
    """Main function to handle command-line execution."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Convert LaTeX .bbl bibliography files to BibTeX .bib format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python bblbib.py                    # Convert main.bbl to main.bib
  python bblbib.py -i refs.bbl        # Convert refs.bbl to refs.bib
  python bblbib.py -i refs.bbl -o bibliography.bib  # Specify output file
        """
    )

    parser.add_argument(
        '-i', '--input',
        default='main.bbl',
        help='Input .bbl file (default: main.bbl)'
    )

    parser.add_argument(
        '-o', '--output',
        help='Output .bib file (default: input filename with .bib extension)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )

    args = parser.parse_args()

    # Check if input file exists
    if not Path(args.input).exists():
        print(f"✗ Error: Input file '{args.input}' not found")
        print(f"  Make sure the file exists in the current directory: {Path.cwd()}")
        return 1

    try:
        converter = BBLToBibConverter()
        converter.convert_bbl_to_bib(args.input, args.output)
        return 0
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())