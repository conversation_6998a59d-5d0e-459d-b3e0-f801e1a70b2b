{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4f2dbe07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用设备: cuda\n", "开始GPU密集计算...\n", "完成 1 次迭代，耗时: 0.07秒\n", "完成 101 次迭代，耗时: 0.08秒\n", "完成 201 次迭代，耗时: 0.08秒\n", "完成 301 次迭代，耗时: 0.09秒\n", "完成 401 次迭代，耗时: 1.98秒\n", "完成 501 次迭代，耗时: 5.05秒\n", "完成 601 次迭代，耗时: 8.16秒\n", "完成 701 次迭代，耗时: 11.26秒\n", "完成 801 次迭代，耗时: 14.38秒\n", "完成 901 次迭代，耗时: 17.51秒\n", "完成 1001 次迭代，耗时: 20.67秒\n", "完成 1101 次迭代，耗时: 23.80秒\n", "完成 1201 次迭代，耗时: 26.94秒\n", "完成 1301 次迭代，耗时: 30.09秒\n", "完成 1401 次迭代，耗时: 33.24秒\n", "完成 1501 次迭代，耗时: 36.40秒\n", "完成 1601 次迭代，耗时: 39.57秒\n", "完成 1701 次迭代，耗时: 42.74秒\n", "完成 1801 次迭代，耗时: 45.91秒\n", "完成 1901 次迭代，耗时: 49.09秒\n", "完成 2001 次迭代，耗时: 52.26秒\n", "完成 2101 次迭代，耗时: 55.44秒\n", "完成 2201 次迭代，耗时: 58.62秒\n", "完成 2301 次迭代，耗时: 61.80秒\n", "完成 2401 次迭代，耗时: 64.97秒\n", "完成 2501 次迭代，耗时: 68.15秒\n", "完成 2601 次迭代，耗时: 71.32秒\n", "完成 2701 次迭代，耗时: 74.50秒\n", "完成 2801 次迭代，耗时: 77.69秒\n", "完成 2901 次迭代，耗时: 80.86秒\n", "完成 3001 次迭代，耗时: 84.04秒\n", "完成 3101 次迭代，耗时: 87.22秒\n", "完成 3201 次迭代，耗时: 90.39秒\n", "完成 3301 次迭代，耗时: 93.57秒\n", "完成 3401 次迭代，耗时: 96.75秒\n", "完成 3501 次迭代，耗时: 99.92秒\n", "完成 3601 次迭代，耗时: 103.09秒\n", "完成 3701 次迭代，耗时: 106.27秒\n", "完成 3801 次迭代，耗时: 109.44秒\n", "完成 3901 次迭代，耗时: 112.61秒\n", "完成 4001 次迭代，耗时: 115.78秒\n", "完成 4101 次迭代，耗时: 118.95秒\n", "完成 4201 次迭代，耗时: 122.12秒\n", "完成 4301 次迭代，耗时: 125.28秒\n", "完成 4401 次迭代，耗时: 128.45秒\n", "完成 4501 次迭代，耗时: 131.61秒\n", "完成 4601 次迭代，耗时: 134.78秒\n", "完成 4701 次迭代，耗时: 137.94秒\n", "完成 4801 次迭代，耗时: 141.10秒\n", "完成 4901 次迭代，耗时: 144.26秒\n", "完成 5001 次迭代，耗时: 147.42秒\n", "完成 5101 次迭代，耗时: 150.58秒\n", "完成 5201 次迭代，耗时: 153.73秒\n", "完成 5301 次迭代，耗时: 156.88秒\n", "完成 5401 次迭代，耗时: 160.04秒\n", "完成 5501 次迭代，耗时: 163.19秒\n", "完成 5601 次迭代，耗时: 166.33秒\n", "完成 5701 次迭代，耗时: 169.47秒\n", "完成 5801 次迭代，耗时: 172.61秒\n", "完成 5901 次迭代，耗时: 175.75秒\n", "完成 6001 次迭代，耗时: 178.89秒\n", "完成 6101 次迭代，耗时: 182.04秒\n", "完成 6201 次迭代，耗时: 185.18秒\n", "完成 6301 次迭代，耗时: 188.32秒\n", "完成 6401 次迭代，耗时: 191.45秒\n", "完成 6501 次迭代，耗时: 194.59秒\n", "完成 6601 次迭代，耗时: 197.72秒\n", "完成 6701 次迭代，耗时: 200.87秒\n", "完成 6801 次迭代，耗时: 204.01秒\n", "完成 6901 次迭代，耗时: 207.15秒\n", "完成 7001 次迭代，耗时: 210.28秒\n", "完成 7101 次迭代，耗时: 213.41秒\n", "完成 7201 次迭代，耗时: 216.53秒\n", "完成 7301 次迭代，耗时: 219.66秒\n", "完成 7401 次迭代，耗时: 222.80秒\n", "完成 7501 次迭代，耗时: 225.93秒\n", "完成 7601 次迭代，耗时: 229.06秒\n", "完成 7701 次迭代，耗时: 232.19秒\n", "完成 7801 次迭代，耗时: 235.33秒\n", "完成 7901 次迭代，耗时: 238.46秒\n", "完成 8001 次迭代，耗时: 241.59秒\n", "完成 8101 次迭代，耗时: 244.71秒\n", "完成 8201 次迭代，耗时: 247.83秒\n", "完成 8301 次迭代，耗时: 250.96秒\n", "完成 8401 次迭代，耗时: 254.08秒\n", "完成 8501 次迭代，耗时: 257.20秒\n", "完成 8601 次迭代，耗时: 260.33秒\n", "完成 8701 次迭代，耗时: 263.48秒\n", "完成 8801 次迭代，耗时: 266.62秒\n", "完成 8901 次迭代，耗时: 269.76秒\n", "完成 9001 次迭代，耗时: 272.92秒\n", "完成 9101 次迭代，耗时: 276.06秒\n", "完成 9201 次迭代，耗时: 279.20秒\n", "完成 9301 次迭代，耗时: 282.35秒\n", "完成 9401 次迭代，耗时: 285.49秒\n", "完成 9501 次迭代，耗时: 288.65秒\n", "完成 9601 次迭代，耗时: 291.79秒\n", "完成 9701 次迭代，耗时: 294.95秒\n", "完成 9801 次迭代，耗时: 298.10秒\n", "完成 9901 次迭代，耗时: 301.24秒\n", "计算完成！\n"]}], "source": ["import torch\n", "import time\n", "\n", "# 检查GPU可用性\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "# 创建大矩阵进行密集计算\n", "size = 4096\n", "a = torch.randn(size, size, device=device)\n", "b = torch.randn(size, size, device=device)\n", "\n", "print(\"开始GPU密集计算...\")\n", "start_time = time.time()\n", "\n", "# 持续进行矩阵乘法运算\n", "for i in range(10000):\n", "    c = torch.mm(a, b)\n", "    a = c + torch.randn(size, size, device=device)\n", "    \n", "    if i % 100 == 0:\n", "        elapsed = time.time() - start_time\n", "        print(f\"完成 {i+1} 次迭代，耗时: {elapsed:.2f}秒\")\n", "\n", "print(\"计算完成！\")"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}