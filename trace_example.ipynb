{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b80f43e9", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "sys.path.append(\"./EasyTSAD-bench\")\n", "sys.path.insert(0, \"./Trace-main\")\n", "os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'\n", "os.environ['TRACE_CUSTOMLLM_URL'] = \"http://21.91.109.192:30000/v1\"\n", "os.environ['TRACE_CUSTOMLLM_MODEL'] = \"zai-org/GLM-4.5V\"#\"deepseek-ai/DeepSeek-V3\"\n", "os.environ['TRACE_CUSTOMLLM_API_KEY'] = \"sk-dum7ge6k3FR8ThN7xZmnaWxjv1PPv9OsO7JMjbOu5tVqxDEt\""]}, {"cell_type": "code", "execution_count": 2, "id": "bae307e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modified Trace\n", "MessageNode: (eval:0, dtype=<class 'list'>, data=[1, 2, 3, 4])\n"]}], "source": ["from opto.trace import node, bundle\n", "\n", "@bundle(trainable=True)\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "test_input = [1, 2, 3, 4]\n", "test_output = strange_sort_list(test_input)\n", "print(test_output)"]}, {"cell_type": "code", "execution_count": 4, "id": "acb90210", "metadata": {}, "outputs": [], "source": ["policy = Policy()"]}, {"cell_type": "code", "execution_count": 5, "id": "406d4b97", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<opto.trace.nodes.ParameterNode at 0x1095ee4e0>,\n", " <opto.trace.nodes.ParameterNode at 0x1095ee4e0>]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["policy.strange_sort_list.parameters()"]}, {"cell_type": "code", "execution_count": 6, "id": "6a308a91", "metadata": {}, "outputs": [], "source": ["def get_feedback(predict, target):\n", "    if predict == target:\n", "        return \"test case passed!\"\n", "    else:\n", "        return \"test case failed!\""]}, {"cell_type": "code", "execution_count": 7, "id": "6c7caa32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using CustomLLM as the default LLM backend.\n", "Training Epoch 0\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval1 = eval(self=Policy0, lst=lst1, __code=__code2)\n", "eq0 = eq(x=eval1, y=list0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[eq] This is an eq operator of x and y..\n", "\n", "#Variables\n", "(code) __code2:def strange_sort_list(self, lst):\n", "        '''\n", "        Given list of integers, return list in strange order.\n", "        Strange sorting, is when you start with the minimum value,\n", "        then maximum of the remaining integers, then minimum and so on.\n", "\n", "        Examples:\n", "        strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "        strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "        strange_sort_list([]) == []\n", "        '''\n", "        lst = sorted(lst)\n", "        return lst\n", "\n", "#Constraints\n", "(code) __code2: The code should start with:\n", "def strange_sort_list(self, lst):\n", "\n", "#Inputs\n", "(Policy) Policy0=<__main__.Policy object at 0x1095ef7d0>\n", "(list) lst1=[1, 2, 3, 4]\n", "(list) list0=[1, 4, 2, 3]\n", "\n", "#Others\n", "(list) eval1=[1, 2, 3, 4]\n", "\n", "#Outputs\n", "(bool) eq0=False\n", "\n", "#Feedback\n", "test case failed!\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " \n", "<|begin_of_box|>{\n", "\"reasoning\": \"1. The instruction is to modify the value of variables in #Variables to improve the output according to the feedback. 2. The issue is that the current implementation of __code2 in #Variables only sorts the list in ascending order, but it should implement the 'strange sort' algorithm as described in its docstring. The expected output for [1, 2, 3, 4] should be [1, 4, 2, 3], but the current code returns [1, 2, 3, 4], causing eq0 to be False. 3. I need to fix the __code2 function to properly implement the strange sort algorithm, which alternates between selecting the minimum and maximum values from the remaining list.\",\n", "\"answer\": \"The current implementation of __code2 only sorts the list in ascending order, but it needs to implement the strange sort algorithm. I should modify __code2 to properly alternate between minimum and maximum values.\",\n", "\"suggestion\": {\n", "    \"__code2\": \"def strange_sort_list(self, lst):\\n        '''\\n        Given list of integers, return list in strange order.\\n        Strange sorting, is when you start with the minimum value,\\n        then maximum of the remaining integers, then minimum and so on.\\n\\n        Examples:\\n        strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\\n        strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\\n        strange_sort_list([]) == []\\n        '''\\n        result = []\\n        sorted_lst = sorted(lst)\\n        while sorted_lst:\\n            result.append(sorted_lst.pop(0))  # Add minimum\\n            if sorted_lst:\\n                result.append(sorted_lst.pop())  # Add maximum\\n        return result\"\n", "}\n", "}<|end_of_box|>\n", "Training Epoch 1\n"]}], "source": ["from opto.optimizers import OptoPrime\n", "from opto import trace\n", "\n", "test_ground_truth = [1, 4, 2, 3]\n", "test_input = [1, 2, 3, 4]\n", "\n", "epoch = 2\n", "\n", "optimizer = OptoPrime(policy.strange_sort_list.parameters())\n", "\n", "for i in range(epoch):\n", "    print(f\"Training Epoch {i}\")\n", "    try:\n", "        test_output = policy.strange_sort_list(test_input)\n", "        feedback = get_feedback(test_output, test_ground_truth)\n", "    except trace.ExecutionError as e:\n", "        feedback = e.exception_node.data\n", "        test_output = e.exception_node\n", "    \n", "    correctness = test_output.eq(test_ground_truth)\n", "    \n", "    if correctness:\n", "        break\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(correctness, feedback)\n", "    optimizer.step(verbose=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "7bfc966d", "metadata": {}, "outputs": [{"data": {"text/plain": ["<opto.trace.nodes.MessageNode at 0x11c0afc80>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["test_output\n"]}, {"cell_type": "code", "execution_count": 8, "id": "5cd7259c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n<|begin_of_box|>{\"reasoning\": \"The problem is that the current implementation of __code2 doesn\\'t match the expected \\'strange_sort_list\\' behavior. The current code simply sorts the list in ascending order, but the expected result should be [1, 4, 2, 3] for input [1, 2, 3, 4]. According to the docstring, the strange_sort_list function should alternately pick the minimum and maximum values from the remaining list. I need to change the implementation of __code2 to properly implement this logic.\", \"answer\": \"The current implementation of strange_sort_list is incorrect. It should alternately pick the minimum and maximum values from the sorted list.\", \"suggestion\": {\"__code2\": \"def strange_sort_list(self, lst):\\\\n        \\'\\'\\'\\\\n        Given list of integers, return list in strange order.\\\\n        Strange sorting, is when you start with the minimum value,\\\\n        then maximum of the remaining integers, then minimum and so on.\\\\n\\\\n        Examples:\\\\n        strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\\\\n        strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\\\\n        strange_sort_list([]) == []\\\\n        \\'\\'\\'\\\\n        if not lst:\\\\n            return []\\\\n        \\\\n        sorted_lst = sorted(lst)\\\\n        result = []\\\\n        left, right = 0, len(sorted_lst) - 1\\\\n        \\\\n        while left <= right:\\\\n            result.append(sorted_lst[left])\\\\n            left += 1\\\\n            if left <= right:\\\\n                result.append(sorted_lst[right])\\\\n                right -= 1\\\\n                \\\\n        return result\"}}<|end_of_box|>'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["optimizer.log[0][\"response\"]"]}, {"cell_type": "code", "execution_count": 9, "id": "8420020c", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <style>\n", "        :root {\n", "            --text-color: #1c1c1c;\n", "            --bg-color: #ffffff;\n", "            --trace-bg: #e0e0e0;\n", "            --trace-border: #9e9e9e;\n", "            --feedback-bg: #ffb3ba;\n", "            --feedback-border: #ff6b6b;\n", "            --reason-bg: #baffc9;\n", "            --reason-border: #4caf50;\n", "            --improve-bg: #ffffff;\n", "            --improve-border: #4d9de0;\n", "        }\n", "        @media (prefers-color-scheme: dark) {\n", "            :root {\n", "                --text-color: #e0e0e0;\n", "                --bg-color: #121212;\n", "                --trace-bg: #2a2a2a;\n", "                --trace-border: #555555;\n", "                --feedback-bg: #5c2b30;\n", "                --feedback-border: #ff6b6b;\n", "                --reason-bg: #1e3a2b;\n", "                --reason-border: #4caf50;\n", "                --improve-bg: #121212;\n", "                --improve-border: #4d9de0;\n", "            }\n", "        }\n", "    </style>\n", "\n", "    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px; color: var(--text-color);\">\n", "    \n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--trace-bg); border: 2px solid var(--trace-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "                <p><b><PERSON></b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; color: var(--text-color);\">\n", "\n", "#Code\n", "eval1 = eval(self=Policy0, lst=lst1, __code=__code2)\n", "eq0 = eq(x=eval1, y=list0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[eq] This is an eq operator of x and y..\n", "\n", "#Variables\n", "(code) __code2:def strange_sort_list(self, lst):\n", "        '''\n", "        Given list of integers, return list in strange order.\n", "        Strange sorting, is when you start with the minimum value,\n", "        then maximum of the remaining integers, then minimum and so on.\n", "\n", "        Examples:\n", "        strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "        strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "        strange_sort_list([]) == []\n", "        '''\n", "        lst = sorted(lst)\n", "        return lst\n", "\n", "#Constraints\n", "(code) __code2: The code should start with:\n", "def strange_sort_list(self, lst):\n", "\n", "#Inputs\n", "(Policy) Policy0=<__main__.Policy object at 0x1095ef7d0>\n", "(list) lst1=[1, 2, 3, 4]\n", "(list) list0=[1, 4, 2, 3]\n", "\n", "#Outputs\n", "(bool) eq0=False\n", "\n", "</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--trace-border);\">\n", "                g<sub>0</sub>\n", "            </div>\n", "        </div>\n", "        \n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--feedback-bg); border: 2px solid var(--feedback-border); padding: 10px; border-radius: 5px;\">\n", "            <p style=\"margin: 0;\"><b>Feedback: </b>test case failed!</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--feedback-border);\">\n", "            f<sub>0</sub>\n", "        </div>\n", "    </div>\n", "\n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--reason-bg); border: 2px solid var(--reason-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "            <p style=\"margin: 0;\"><b>Reasoning: </b>1. The instruction is to modify the value of variables in #Variables to improve the output according to the feedback. 2. The issue is that the current implementation of __code2 in #Variables only sorts the list in ascending order, but it should implement the 'strange sort' algorithm as described in its docstring. The expected output for [1, 2, 3, 4] should be [1, 4, 2, 3], but the current code returns [1, 2, 3, 4], causing eq0 to be False. 3. I need to fix the __code2 function to properly implement the strange sort algorithm, which alternates between selecting the minimum and maximum values from the remaining list.</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--reason-border);\">\n", "            r<sub>1</sub>\n", "        </div>\n", "    </div>\n", "    \n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 20px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--improve-bg); border: 2px solid var(--improve-border); padding: 10px; border-radius: 5px;\">\n", "                <p><b>Improvement</b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: var(--improve-bg); color: var(--text-color);\">__code2:\n", "\n", "def strange_sort_list(self, lst):\n", "        '''\n", "        Given list of integers, return list in strange order.\n", "        Strange sorting, is when you start with the minimum value,\n", "        then maximum of the remaining integers, then minimum and so on.\n", "\n", "        Examples:\n", "        strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "        strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "        strange_sort_list([]) == []\n", "        '''\n", "        result = []\n", "        sorted_lst = sorted(lst)\n", "        while sorted_lst:\n", "            result.append(sorted_lst.pop(0))  # Add minimum\n", "            if sorted_lst:\n", "                result.append(sorted_lst.pop())  # Add maximum\n", "        return result\n", "\n", "</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--improve-border);\">\n", "                a<sub>1</sub>\n", "            </div>\n", "        </div>\n", "        </div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def render_opt_step(step_idx, optimizer, no_trace_graph=False, no_improvement=False):\n", "    import json\n", "    import re\n", "    from IPython.display import display, HTML\n", "\n", "    idx = step_idx\n", "    response = optimizer.log[idx][\"response\"]\n", "    attempt_n = 0\n", "    while attempt_n < 2:\n", "        try:\n", "            llm_response = json.loads(response)\n", "            break\n", "        except json.JSONDecodeError:\n", "            # Remove things outside the brackets\n", "            response = re.findall(r\"{.*}\", response, re.DOTALL)\n", "            if len(response) > 0:\n", "                response = response[0]\n", "            attempt_n += 1\n", "        except Exception:\n", "            attempt_n += 1\n", "    r1 = llm_response[\"reasoning\"]\n", "\n", "    if llm_response.get(\"suggestion\"):\n", "        a1 = \"\".join(\n", "            [\n", "                f\"{var_name}:\\n\\n{var_body}\\n\\n\"\n", "                for var_name, var_body in llm_response[\"suggestion\"].items()\n", "            ]\n", "        )\n", "    elif llm_response.get(\"answer\") is not None:\n", "        a1 = llm_response[\"answer\"]\n", "    else:\n", "        a1 = \"<ERROR> NULL/INVALID RESPONSE\"\n", "\n", "    pi = optimizer.summary_log[idx][\"problem_instance\"]  # full\n", "    f1 = pi.feedback\n", "\n", "    masked = [\"#Feedback\", \"#Others\", \"#Instruction\"]\n", "    pi = optimizer.problem_instance(optimizer.summary_log[idx][\"summary\"], mask=masked)\n", "\n", "    # a hack to remove \"#Feedback:\" because it has a colon\n", "    pi = str(pi)\n", "    pi = pi.replace(\"#Feedback:\", \"#Feedback\")\n", "\n", "    for m in masked:\n", "        pi = pi.replace(m + \"\\n\", \"\")\n", "\n", "    # a quick processing to reduce multiple empty lines to one\n", "    pi = re.sub(r\"\\n\\s*\\n\", \"\\n\\n\", pi)\n", "    g1 = pi\n", "\n", "    html_template = f\"\"\"\n", "    <style>\n", "        :root {{\n", "            --text-color: #1c1c1c;\n", "            --bg-color: #ffffff;\n", "            --trace-bg: #e0e0e0;\n", "            --trace-border: #9e9e9e;\n", "            --feedback-bg: #ffb3ba;\n", "            --feedback-border: #ff6b6b;\n", "            --reason-bg: #baffc9;\n", "            --reason-border: #4caf50;\n", "            --improve-bg: #ffffff;\n", "            --improve-border: #4d9de0;\n", "        }}\n", "        @media (prefers-color-scheme: dark) {{\n", "            :root {{\n", "                --text-color: #e0e0e0;\n", "                --bg-color: #121212;\n", "                --trace-bg: #2a2a2a;\n", "                --trace-border: #555555;\n", "                --feedback-bg: #5c2b30;\n", "                --feedback-border: #ff6b6b;\n", "                --reason-bg: #1e3a2b;\n", "                --reason-border: #4caf50;\n", "                --improve-bg: #121212;\n", "                --improve-border: #4d9de0;\n", "            }}\n", "        }}\n", "    </style>\n", "\n", "    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px; color: var(--text-color);\">\n", "    \"\"\"\n", "\n", "    if not no_trace_graph:\n", "        html_template += f\"\"\"\n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--trace-bg); border: 2px solid var(--trace-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "                <p><b><PERSON></b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; color: var(--text-color);\">{g1}</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--trace-border);\">\n", "                g<sub>{idx}</sub>\n", "            </div>\n", "        </div>\n", "        \"\"\"\n", "\n", "    html_template += f\"\"\"\n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--feedback-bg); border: 2px solid var(--feedback-border); padding: 10px; border-radius: 5px;\">\n", "            <p style=\"margin: 0;\"><b>Feedback: </b>{f1}</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--feedback-border);\">\n", "            f<sub>{idx}</sub>\n", "        </div>\n", "    </div>\n", "\n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--reason-bg); border: 2px solid var(--reason-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "            <p style=\"margin: 0;\"><b>Reasoning: </b>{r1}</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--reason-border);\">\n", "            r<sub>{idx + 1}</sub>\n", "        </div>\n", "    </div>\n", "    \"\"\"\n", "\n", "    if not no_improvement:\n", "        html_template += f\"\"\"\n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 20px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--improve-bg); border: 2px solid var(--improve-border); padding: 10px; border-radius: 5px;\">\n", "                <p><b>Improvement</b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: var(--improve-bg); color: var(--text-color);\">{a1}</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--improve-border);\">\n", "                a<sub>{idx + 1}</sub>\n", "            </div>\n", "        </div>\n", "        \"\"\"\n", "\n", "    html_template += \"</div>\"\n", "\n", "    display(HTML(html_template))\n", "\n", "render_opt_step(0, optimizer)"]}, {"cell_type": "code", "execution_count": 3, "id": "57aca232", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3, 4])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "a = np.array([1, 2, 3, 4])\n", "b = np.array([5, 6, 7, 8])\n", "np.min([a, b],axis =0)"]}, {"cell_type": "code", "execution_count": 2, "id": "d448ce49", "metadata": {}, "outputs": [], "source": ["from opto.trace import node, GRAPH\n", "def print_node(node):\n", "    print(node)\n", "    print(f\"parents: {[p.name for p in node.parents]}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "7b6ba449", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (divide:2, dtype=<class 'float'>, data=0.3333333333333333)\n", "MessageNode: (divide:2, dtype=<class 'float'>, data=0.3333333333333333)\n", "parents: ['node_x:1', 'node_y:1']\n", "\n", "\n", "MessageNode: (getitem:1, dtype=<class 'int'>, data=1)\n", "parents: ['dict_node:1', 'str:1']\n", "len(dict_node) = MessageNode: (len_:1, dtype=<class 'int'>, data=2)\n", "\n", "\n"]}], "source": ["# Basic arithmetic operations\n", "x = node(1, name=\"node_x\")\n", "y = node(3, name=\"node_y\")\n", "z = x / y\n", "z2 = x / 3  # the int 3 would be converted to a node automatically\n", "print(z)\n", "print_node(z)\n", "print(\"\\n\")\n", "\n", "# Index a node\n", "dict_node = node({\"a\": 1, \"b\": 2}, name=\"dict_node\")\n", "a = dict_node[\"a\"]\n", "print_node(a)\n", "print(\"len(dict_node) =\", dict_node.len())\n", "\n", "print(\"\\n\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d3378809", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4078a5e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "05d4e198", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "id": "6059d5be", "metadata": {}, "outputs": [], "source": ["import random\n", "import numpy as np\n", "from opto.trace import bundle, node, Module, GRAPH\n", "from opto.trace.errors import ExecutionError\n", "from opto.trace.bundle import ExceptionNode\n", "from opto.optimizers import OptoPrime"]}, {"cell_type": "code", "execution_count": 17, "id": "294f79bf", "metadata": {}, "outputs": [], "source": ["def create_battleship_board(width, height):\n", "    board = [['.' for _ in range(width)] for _ in range(height)]\n", "    return board\n", "\n", "def can_place_ship(board, row, col, size, is_vertical):\n", "    if is_vertical:\n", "        if row + size > len(board):\n", "            return False\n", "        for i in range(size):\n", "            if board[row + i][col] != '.':\n", "                return False\n", "    else:\n", "        if col + size > len(board[0]):\n", "            return False\n", "        for i in range(size):\n", "            if board[row][col + i] != '.':\n", "                return False\n", "    return True\n", "\n", "def place_ship(board, row, col, size, is_vertical, ship_symbol):\n", "    if is_vertical:\n", "        for i in range(size):\n", "            board[row + i][col] = ship_symbol\n", "    else:\n", "        for i in range(size):\n", "            board[row][col + i] = ship_symbol\n", "\n", "def create_and_fill_battleship_board(width, height, ships, num_each_type=2):\n", "    board = [['.' for _ in range(width)] for _ in range(height)]\n", "    for ship_symbol, size in ships.items():\n", "        for num in range(1, num_each_type + 1):\n", "            placed = False\n", "            while not placed:\n", "                row = random.randint(0, height - 1)\n", "                col = random.randint(0, width - 1)\n", "                is_vertical = random.choice([True, False])\n", "                if can_place_ship(board, row, col, size, is_vertical):\n", "                    place_ship(board, row, col, size, is_vertical, ship_symbol)\n", "                    placed = True\n", "    return board\n", "\n", "def check_hit(board, row, col):\n", "    if 0 <= row < len(board) and 0 <= col < len(board[0]):\n", "        if board[row][col] not in ['.', 'O', 'X']:\n", "            board[row][col] = 'X'\n", "            return True\n", "        else:\n", "            if board[row][col] == '.':\n", "                board[row][col] = 'O'\n", "    return False\n", "\n", "# Ships to be placed on the board\n", "ships = {\n", "    'C': 5,  # Carrier\n", "    'B': 4,  # Battleship\n", "    'R': 3,  # Cruiser\n", "    'S': 3,  # Submarine\n", "    'D': 2  # Destroyer\n", "}"]}, {"cell_type": "code", "execution_count": 18, "id": "eb7ed2f0", "metadata": {}, "outputs": [], "source": ["# Define BattleshipBoard class\n", "class BattleshipBoard:\n", "    def __init__(self, width, height, num_each_type=2, exclude_ships=[], init_with_one_hit=False):\n", "        self.width = width\n", "        self.height = height\n", "        self.ships = {s: ships[s] for s in ships if s not in exclude_ships}\n", "        self.board = create_and_fill_battleship_board(width, height, self.ships, num_each_type=num_each_type)\n", "        self.shots = [['.' for _ in range(width)] for _ in range(height)]\n", "        self.hits = 0\n", "        self.misses = 0\n", "\n", "        if init_with_one_hit:\n", "            initialized = False\n", "            for row in range(height):\n", "                for col in range(width):\n", "                    if self.board[row][col] != '.':\n", "                        self.check_shot(row, col)\n", "                        initialized = True\n", "                        break\n", "                if initialized:\n", "                    break\n", "\n", "    def get_life_points(self):\n", "        return sum(self.ships.values())\n", "\n", "    def check_shot(self, row, col):\n", "        is_hit = check_hit(self.board, row, col)\n", "        if is_hit:\n", "            self.hits += 1\n", "            self.shots[row][col] = 'X'\n", "        else:\n", "            self.misses += 1\n", "            if self.shots[row][col] == '.':\n", "                self.shots[row][col] = 'O'\n", "        return is_hit\n", "\n", "    def check_terminate(self):\n", "        return (self.hits >= sum(self.ships.values())) or (self.misses + self.hits >= self.width * self.height)\n", "\n", "    def get_board(self):\n", "        return self.board\n", "\n", "    def get_shots(self):\n", "        return self.shots\n", "\n", "    def get_shots_overlay_board(self):\n", "        shots_overlay_board = [[self.board[row][col] if self.shots[row][col] == '.' else self.shots[row][col] for col in range(self.width)] for row in range(self.height)]\n", "        return shots_overlay_board\n", "\n", "    def get_hits(self):\n", "        return self.hits\n", "\n", "    def get_misses(self):\n", "        return self.misses\n", "\n", "    def get_game_status(self):\n", "        if self.hits == sum(self.ships.values()):\n", "            return 'Game Over: All ships sunk!'\n", "        return 'Game in progress'\n", "\n", "    def visualize_board(self):\n", "        str_rep = ''\n", "        for row in self.board:\n", "            str_rep += ' '.join(row) + '\\n'\n", "        print(str_rep)\n", "\n", "    def visualize_own_board(self):\n", "        str_rep = ''\n", "        board = self.get_shots_overlay_board()\n", "        for row in board:\n", "            str_rep += ' '.join(row) + '\\n'\n", "        print(str_rep)\n", "\n", "    def visualize_shots(self):\n", "        str_rep = ''\n", "        for row in self.shots:\n", "            str_rep += ' '.join(row) + '\\n'\n", "        print(str_rep)"]}, {"cell_type": "code", "execution_count": 19, "id": "9b5e6fd3", "metadata": {}, "outputs": [], "source": ["# Define Policy class\n", "class Policy(Module):\n", "    def init(self, width, height):\n", "        pass\n", "\n", "    def __call__(self, map):\n", "        return self.select_coordinate(map).data\n", "\n", "    def select_coordinate(self, map):\n", "        plan = self.reason(map)\n", "        output = self.act(map, plan)\n", "        return output\n", "\n", "    @bundle(trainable=True)\n", "    def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        return\n", "\n", "    @bundle(trainable=True)\n", "    def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        return"]}, {"cell_type": "code", "execution_count": 20, "id": "ace8839c", "metadata": {}, "outputs": [], "source": ["# Function to get user feedback for placing shot\n", "def user_fb_for_placing_shot(board, coords):\n", "    try:\n", "        reward = board.check_shot(coords[0], coords[1])\n", "        new_map = board.get_shots()\n", "        terminal = board.check_terminate()\n", "        return new_map, reward, terminal, f\"Got {int(reward)} reward.\"\n", "    except Exception as e:\n", "        board.misses += 1\n", "        return board.get_shots(), 0, <PERSON>alse, str(e)\n", "    \n", "# Function to rollout policy\n", "def rollout(policy, board):\n", "    rewards = []\n", "    obs = board.get_shots()\n", "    while not board.check_terminate():\n", "        output = policy(obs)\n", "        obs, reward, terminal, feedback = user_fb_for_placing_shot(board, output)\n", "        if terminal:\n", "            break\n", "        rewards.append(reward)\n", "    rewards = np.array(rewards)\n", "    return rewards\n", "\n", "# Function to evaluate policy\n", "def eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes):\n", "    scores = []\n", "    for _ in range(n_eval_episodes):\n", "        board = BattleshipBoard(board_size, board_size, num_each_type=num_each_type, exclude_ships=exclude_ships)\n", "        rewards = rollout(policy, board)\n", "        scores.append(rewards.mean())\n", "    scores = np.array(scores)\n", "    print(f\"Scores: {scores.mean()} ({scores.std()})\")\n", "    return scores"]}, {"cell_type": "code", "execution_count": 21, "id": "e3006808", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scores: 0.0 (0.0)\n", "Initial scores: [0. 0. 0.]\n"]}], "source": ["# Set parameters\n", "board_size = 5\n", "num_each_type = 1\n", "exclude_ships = ['C']\n", "n_eval_episodes = 3\n", "\n", "# Create policy and evaluate\n", "policy = Policy()\n", "init_scores = eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes)\n", "print(\"Initial scores:\", init_scores)"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}