import os
import sys
sys.path.append("./EasyTSAD-bench")
sys.path.insert(0, "./Trace-main")
os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'
os.environ['TRACE_CUSTOMLLM_URL'] = "https://api.siliconflow.cn/v1/"
os.environ['TRACE_CUSTOMLLM_MODEL'] = "deepseek-ai/DeepSeek-V3"#"zai-org/GLM-4.5V"#"deepseek-ai/DeepSeek-V3"
os.environ['TRACE_CUSTOMLLM_API_KEY'] = "sk-dgkfijjrzcwjhjfutwsfqfknqolfqgvfdiiaawpbngkohfvi"

from opto.trace import node, bundle, Module
class Policy(Module):
    def __init__(self):
        pass

    @bundle(trainable=True)
    def simple_ad(self, lst):
        '''
        Given list of float, return list of its anomaly flags.
        Anomaly flag is 1 if the value is an anomaly, 0 otherwise.

        Examples:
        simple_ad([1, 2, 3, 4]) == [0, 0, 0, 0]
        simple_ad([5, 5, 5, 5]) == [0, 0, 0, 0]
        simple_ad([1, 2, 3, 4, 10, 10, 7, 8, 9, 10]) == [0, 0, 0, 0, 1, 1, 0, 0, 0, 0]
        simple_ad([]) == []
        '''
        pred = [0] * len(lst)
        return pred

policy = Policy()

policy.parameters()

def get_feedback(predict, target):
    if predict == target:
        return "test case passed!"
    else:
        return "test case failed!"

from opto.optimizers import OptoPrime
from opto import trace

test_ground_truth = [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1]
test_input = [1, 4, 3, 4, 5, 6, 1, 2, 3, 4, 10]

epoch = 5

optimizer = OptoPrime(policy.parameters())

for i in range(epoch):
    print(f"Training Epoch {i}")
    try:
        test_output = policy.simple_ad(test_input)
        feedback = get_feedback(test_output, test_ground_truth)
    except trace.ExecutionError as e:
        feedback = e.exception_node.data
        test_output = e.exception_node
    
    correctness = test_output.eq(test_ground_truth)
    
    if correctness:
        break

    optimizer.zero_feedback()
    optimizer.backward(correctness, feedback)
    optimizer.step(verbose=True)

test_output.data


optimizer.log[0]["response"]

def render_opt_step(step_idx, optimizer, no_trace_graph=False, no_improvement=False):
    import json
    import re
    from IPython.display import display, HTML

    idx = step_idx
    response = optimizer.log[idx]["response"]
    attempt_n = 0
    while attempt_n < 2:
        try:
            llm_response = json.loads(response)
            break
        except json.JSONDecodeError:
            # Remove things outside the brackets
            response = re.findall(r"{.*}", response, re.DOTALL)
            if len(response) > 0:
                response = response[0]
            attempt_n += 1
        except Exception:
            attempt_n += 1
    r1 = llm_response["reasoning"]

    if llm_response.get("suggestion"):
        a1 = "".join(
            [
                f"{var_name}:\n\n{var_body}\n\n"
                for var_name, var_body in llm_response["suggestion"].items()
            ]
        )
    elif llm_response.get("answer") is not None:
        a1 = llm_response["answer"]
    else:
        a1 = "<ERROR> NULL/INVALID RESPONSE"

    pi = optimizer.summary_log[idx]["problem_instance"]  # full
    f1 = pi.feedback

    masked = ["#Feedback", "#Others", "#Instruction"]
    pi = optimizer.problem_instance(optimizer.summary_log[idx]["summary"], mask=masked)

    # a hack to remove "#Feedback:" because it has a colon
    pi = str(pi)
    pi = pi.replace("#Feedback:", "#Feedback")

    for m in masked:
        pi = pi.replace(m + "\n", "")

    # a quick processing to reduce multiple empty lines to one
    pi = re.sub(r"\n\s*\n", "\n\n", pi)
    g1 = pi

    html_template = f"""
    <style>
        :root {{
            --text-color: #1c1c1c;
            --bg-color: #ffffff;
            --trace-bg: #e0e0e0;
            --trace-border: #9e9e9e;
            --feedback-bg: #ffb3ba;
            --feedback-border: #ff6b6b;
            --reason-bg: #baffc9;
            --reason-border: #4caf50;
            --improve-bg: #ffffff;
            --improve-border: #4d9de0;
        }}
        @media (prefers-color-scheme: dark) {{
            :root {{
                --text-color: #e0e0e0;
                --bg-color: #121212;
                --trace-bg: #2a2a2a;
                --trace-border: #555555;
                --feedback-bg: #5c2b30;
                --feedback-border: #ff6b6b;
                --reason-bg: #1e3a2b;
                --reason-border: #4caf50;
                --improve-bg: #121212;
                --improve-border: #4d9de0;
            }}
        }}
    </style>

    <div style="font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px; color: var(--text-color);">
    """

    if not no_trace_graph:
        html_template += f"""
        <div style="display: flex; align-items: stretch; margin-bottom: 10px;">
            <div style="flex-grow: 1; background-color: var(--trace-bg); border: 2px solid var(--trace-border); padding: 10px; border-radius: 5px; width: 550px;">
                <p><b>Trace Graph</b></p>
                <pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; color: var(--text-color);">{g1}</pre>
            </div>
            <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--trace-border);">
                g<sub>{idx}</sub>
            </div>
        </div>
        """

    html_template += f"""
    <div style="display: flex; align-items: stretch; margin-bottom: 10px;">
        <div style="flex-grow: 1; background-color: var(--feedback-bg); border: 2px solid var(--feedback-border); padding: 10px; border-radius: 5px;">
            <p style="margin: 0;"><b>Feedback: </b>{f1}</p>
        </div>
        <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--feedback-border);">
            f<sub>{idx}</sub>
        </div>
    </div>

    <div style="display: flex; align-items: stretch; margin-bottom: 10px;">
        <div style="flex-grow: 1; background-color: var(--reason-bg); border: 2px solid var(--reason-border); padding: 10px; border-radius: 5px; width: 550px;">
            <p style="margin: 0;"><b>Reasoning: </b>{r1}</p>
        </div>
        <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--reason-border);">
            r<sub>{idx + 1}</sub>
        </div>
    </div>
    """

    if not no_improvement:
        html_template += f"""
        <div style="display: flex; align-items: stretch; margin-bottom: 20px;">
            <div style="flex-grow: 1; background-color: var(--improve-bg); border: 2px solid var(--improve-border); padding: 10px; border-radius: 5px;">
                <p><b>Improvement</b></p>
                <pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: var(--improve-bg); color: var(--text-color);">{a1}</pre>
            </div>
            <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--improve-border);">
                a<sub>{idx + 1}</sub>
            </div>
        </div>
        """

    html_template += "</div>"

    display(HTML(html_template))

for i in range(len(optimizer.log)):
    render_opt_step(i, optimizer)

import numpy as np
a = np.array([1, 2, 3, 4])
b = np.array([5, 6, 7, 8])
np.min([a, b],axis =0)

from opto.trace import node, GRAPH
def print_node(node):
    print(node)
    print(f"parents: {[p.name for p in node.parents]}")

# Basic arithmetic operations
x = node(1, name="node_x")
y = node(3, name="node_y")
z = x / y
z2 = x / 3  # the int 3 would be converted to a node automatically
print(z)
print_node(z)
print("\n")

# Index a node
dict_node = node({"a": 1, "b": 2}, name="dict_node")
a = dict_node["a"]
print_node(a)
print("len(dict_node) =", dict_node.len())

print("\n")









import random
import numpy as np
from opto.trace import bundle, node, Module, GRAPH
from opto.trace.errors import ExecutionError
from opto.trace.bundle import ExceptionNode
from opto.optimizers import OptoPrime

def create_battleship_board(width, height):
    board = [['.' for _ in range(width)] for _ in range(height)]
    return board

def can_place_ship(board, row, col, size, is_vertical):
    if is_vertical:
        if row + size > len(board):
            return False
        for i in range(size):
            if board[row + i][col] != '.':
                return False
    else:
        if col + size > len(board[0]):
            return False
        for i in range(size):
            if board[row][col + i] != '.':
                return False
    return True

def place_ship(board, row, col, size, is_vertical, ship_symbol):
    if is_vertical:
        for i in range(size):
            board[row + i][col] = ship_symbol
    else:
        for i in range(size):
            board[row][col + i] = ship_symbol

def create_and_fill_battleship_board(width, height, ships, num_each_type=2):
    board = [['.' for _ in range(width)] for _ in range(height)]
    for ship_symbol, size in ships.items():
        for num in range(1, num_each_type + 1):
            placed = False
            while not placed:
                row = random.randint(0, height - 1)
                col = random.randint(0, width - 1)
                is_vertical = random.choice([True, False])
                if can_place_ship(board, row, col, size, is_vertical):
                    place_ship(board, row, col, size, is_vertical, ship_symbol)
                    placed = True
    return board

def check_hit(board, row, col):
    if 0 <= row < len(board) and 0 <= col < len(board[0]):
        if board[row][col] not in ['.', 'O', 'X']:
            board[row][col] = 'X'
            return True
        else:
            if board[row][col] == '.':
                board[row][col] = 'O'
    return False

# Ships to be placed on the board
ships = {
    'C': 5,  # Carrier
    'B': 4,  # Battleship
    'R': 3,  # Cruiser
    'S': 3,  # Submarine
    'D': 2  # Destroyer
}

# Define BattleshipBoard class
class BattleshipBoard:
    def __init__(self, width, height, num_each_type=2, exclude_ships=[], init_with_one_hit=False):
        self.width = width
        self.height = height
        self.ships = {s: ships[s] for s in ships if s not in exclude_ships}
        self.board = create_and_fill_battleship_board(width, height, self.ships, num_each_type=num_each_type)
        self.shots = [['.' for _ in range(width)] for _ in range(height)]
        self.hits = 0
        self.misses = 0

        if init_with_one_hit:
            initialized = False
            for row in range(height):
                for col in range(width):
                    if self.board[row][col] != '.':
                        self.check_shot(row, col)
                        initialized = True
                        break
                if initialized:
                    break

    def get_life_points(self):
        return sum(self.ships.values())

    def check_shot(self, row, col):
        is_hit = check_hit(self.board, row, col)
        if is_hit:
            self.hits += 1
            self.shots[row][col] = 'X'
        else:
            self.misses += 1
            if self.shots[row][col] == '.':
                self.shots[row][col] = 'O'
        return is_hit

    def check_terminate(self):
        return (self.hits >= sum(self.ships.values())) or (self.misses + self.hits >= self.width * self.height)

    def get_board(self):
        return self.board

    def get_shots(self):
        return self.shots

    def get_shots_overlay_board(self):
        shots_overlay_board = [[self.board[row][col] if self.shots[row][col] == '.' else self.shots[row][col] for col in range(self.width)] for row in range(self.height)]
        return shots_overlay_board

    def get_hits(self):
        return self.hits

    def get_misses(self):
        return self.misses

    def get_game_status(self):
        if self.hits == sum(self.ships.values()):
            return 'Game Over: All ships sunk!'
        return 'Game in progress'

    def visualize_board(self):
        str_rep = ''
        for row in self.board:
            str_rep += ' '.join(row) + '\n'
        print(str_rep)

    def visualize_own_board(self):
        str_rep = ''
        board = self.get_shots_overlay_board()
        for row in board:
            str_rep += ' '.join(row) + '\n'
        print(str_rep)

    def visualize_shots(self):
        str_rep = ''
        for row in self.shots:
            str_rep += ' '.join(row) + '\n'
        print(str_rep)

# Define Policy class
class Policy(Module):
    def init(self, width, height):
        pass

    def __call__(self, map):
        return self.select_coordinate(map).data

    def select_coordinate(self, map):
        plan = self.reason(map)
        output = self.act(map, plan)
        return output

    @bundle(trainable=True)
    def act(self, map, plan):
        """
        Given a map, select a target coordinate in a game.
        X denotes hits, O denotes misses, and . denotes unknown positions.
        """
        return

    @bundle(trainable=True)
    def reason(self, map):
        """
        Given a map, analyze the board in a game.
        X denotes hits, O denotes misses, and . denotes unknown positions.
        """
        return

# Function to get user feedback for placing shot
def user_fb_for_placing_shot(board, coords):
    try:
        reward = board.check_shot(coords[0], coords[1])
        new_map = board.get_shots()
        terminal = board.check_terminate()
        return new_map, reward, terminal, f"Got {int(reward)} reward."
    except Exception as e:
        board.misses += 1
        return board.get_shots(), 0, False, str(e)
    
# Function to rollout policy
def rollout(policy, board):
    rewards = []
    obs = board.get_shots()
    while not board.check_terminate():
        output = policy(obs)
        obs, reward, terminal, feedback = user_fb_for_placing_shot(board, output)
        if terminal:
            break
        rewards.append(reward)
    rewards = np.array(rewards)
    return rewards

# Function to evaluate policy
def eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes):
    scores = []
    for _ in range(n_eval_episodes):
        board = BattleshipBoard(board_size, board_size, num_each_type=num_each_type, exclude_ships=exclude_ships)
        rewards = rollout(policy, board)
        scores.append(rewards.mean())
    scores = np.array(scores)
    print(f"Scores: {scores.mean()} ({scores.std()})")
    return scores

# Set parameters
board_size = 5
num_each_type = 1
exclude_ships = ['C']
n_eval_episodes = 3

# Create policy and evaluate
policy = Policy()
init_scores = eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes)
print("Initial scores:", init_scores)