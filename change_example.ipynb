{"cells": [{"cell_type": "code", "execution_count": 1, "id": "76e98f08", "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 2, "id": "a1973f4c", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "64a63135", "metadata": {}, "outputs": [], "source": ["with open('raw_data/ts.json', 'r') as f:\n", "    data = json.load(f) "]}, {"cell_type": "code", "execution_count": 4, "id": "07fa5b95", "metadata": {}, "outputs": [], "source": ["data['info'] = 'change'"]}, {"cell_type": "code", "execution_count": 5, "id": "8fcfa12c", "metadata": {}, "outputs": [], "source": ["data['id'] = 123"]}, {"cell_type": "code", "execution_count": 6, "id": "7eab019d", "metadata": {}, "outputs": [], "source": ["data['dict'] = {}"]}, {"cell_type": "code", "execution_count": 7, "id": "ad3f5011", "metadata": {}, "outputs": [], "source": ["with open('raw_data/ts_more.json', 'w') as f:\n", "    json.dump(data, f)"]}, {"cell_type": "code", "execution_count": 9, "id": "68e6b142", "metadata": {}, "outputs": [], "source": ["data['ts']['value'][20:40] = [np.nan] * 20\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "id": "81dc4ffd", "metadata": {}, "outputs": [], "source": ["data['ts']['value'][150:200] = [np.nan] * 50"]}, {"cell_type": "code", "execution_count": 12, "id": "f3dfb4fb", "metadata": {}, "outputs": [], "source": ["data['ts']['value'][400:500] = [np.nan] * 100\n", "\n", "data['ts']['value'][700:800] = [np.nan] * 100\n", "\n", "data['ts']['value'][1000:1100] = [np.nan] * 100\n", "\n", "data['ts']['value'][1300:1400] = [np.nan] * 100"]}, {"cell_type": "code", "execution_count": 13, "id": "19dd4826", "metadata": {}, "outputs": [], "source": ["with open('raw_data/ts_nan.json', 'w') as f:\n", "    json.dump(data, f)"]}, {"cell_type": "code", "execution_count": 14, "id": "17ea3f90", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.2341910677954737,\n", " 0.2366572262083881,\n", " 0.2308873461480024,\n", " 0.2174863343949876,\n", " 0.2237215273633553,\n", " 0.2047615774016069,\n", " 0.2272827221533189,\n", " 0.2156250827625339,\n", " 0.2109037077884413,\n", " 0.1972452228928657,\n", " 0.1896389078885645,\n", " 0.1791445507679762,\n", " 0.1784931126966542,\n", " 0.1742370506306488,\n", " 0.1644871941630735,\n", " 0.1557858427816863,\n", " 0.1404770481054616,\n", " 0.1391989886512138,\n", " 0.1319401072849493,\n", " 0.1245168153578097,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " 0.0666318895909282,\n", " 0.0667032375703959,\n", " 0.0646093294842664,\n", " 0.0618856979279139,\n", " 0.0661448620806913,\n", " 0.0609798888006613,\n", " 0.0553496026130695,\n", " 0.0563484743224649,\n", " 0.0516953452415935,\n", " 0.0552317233430558,\n", " 0.050696473532198,\n", " 0.0537675387250478,\n", " 0.0526259710577678,\n", " 0.0517666932200104,\n", " 0.0526042564550927,\n", " 0.0511152551492138,\n", " 0.0491609409347224,\n", " 0.0510687238586678,\n", " 0.0484846861750041,\n", " 0.0466482512315772,\n", " 0.0475540603588298,\n", " 0.0503924690989495,\n", " 0.0484381548844581,\n", " 0.0502776919141315,\n", " 0.0480659045579884,\n", " 0.0504390003894955,\n", " 0.0518814704048283,\n", " 0.0534883509807209,\n", " 0.0553030713214727,\n", " 0.0550704148676918,\n", " 0.0576761671529799,\n", " 0.0569999123932615,\n", " 0.0650746423918282,\n", " 0.068027828315715,\n", " 0.0707266431820951,\n", " 0.0716355543955942,\n", " 0.0757520225894362,\n", " 0.0794993425430551,\n", " 0.0806843394156854,\n", " 0.0866651613270717,\n", " 0.0878966894912988,\n", " 0.0995077975908024,\n", " 0.0895035700671914,\n", " 0.0942497617293651,\n", " 0.1056716425800176,\n", " 0.1096981502780685,\n", " 0.1100455839161419,\n", " 0.1198171549860771,\n", " 0.1272869782039728,\n", " 0.1268681965867469,\n", " 0.1321944783414053,\n", " 0.1380357063809434,\n", " 0.1325915453562713,\n", " 0.1511575303892111,\n", " 0.1617666646937031,\n", " 0.1541355330009688,\n", " 0.1626042279283651,\n", " 0.1721183258558078,\n", " 0.1813098068336785,\n", " 0.1885221569091343,\n", " 0.1891022470012303,\n", " 0.1911744404852731,\n", " 0.2054812613661184,\n", " 0.2130441471656421,\n", " 0.2139499562934202,\n", " 0.2335861610150129,\n", " 0.2308873461480024,\n", " 0.2398927019626382,\n", " 0.2498255815074119,\n", " 0.2570162169805604,\n", " 0.283957834359174,\n", " 0.2877734002054886,\n", " 0.2835855840327043,\n", " 0.2869823682617929,\n", " 0.2966143454592494,\n", " 0.2913563095978121,\n", " 0.2982894719284682,\n", " 0.3127824179727061,\n", " 0.3146684862929256,\n", " 0.3177395514868261,\n", " 0.3259042419802382,\n", " 0.3434713553048334,\n", " 0.3420505998911249,\n", " 0.3410517281817294,\n", " 0.3467502602635932,\n", " 0.3354679732846837,\n", " 0.3485897972932667,\n", " 0.3605235223429727,\n", " 0.3477987653497812,\n", " 0.3565683626244946,\n", " 0.3573128632774341,\n", " 0.3601760887043739,\n", " 0.3532181096862672,\n", " 0.3606631162156615,\n", " 0.3582683057814793,\n", " 0.3531715783946704,\n", " 0.3436791950696926,\n", " 0.3423080730338274,\n", " 0.3498678567480398,\n", " 0.3461701701712135,\n", " 0.3603373971797377,\n", " 0.3456800405747301,\n", " 0.3535221141195158,\n", " 0.3489155163291903,\n", " 0.35735939456798,\n", " 0.3485432660027206,\n", " 0.3590810523276399,\n", " 0.3799736019015408,\n", " 0.3898847668435343,\n", " 0.4037759081929274,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " 0.3359332861932963,\n", " 0.3310226839697223,\n", " 0.3212759295882884,\n", " 0.3179008599611393,\n", " 0.3265804967410073,\n", " 0.3228114621847134,\n", " 0.3274180599750388,\n", " 0.3183196415792059,\n", " 0.3217412424958502,\n", " 0.3232302438017291,\n", " 0.3101549510847429,\n", " 0.314317950569131,\n", " 0.3058740723303412,\n", " 0.3103876075385238,\n", " 0.3069908233097504,\n", " 0.3000328442908031,\n", " 0.3029643156116996,\n", " 0.3098509466514944,\n", " 0.2994527541986335,\n", " 0.2962886264236409,\n", " 0.2977776277295198,\n", " 0.2976380338571463,\n", " 0.3040810665912138,\n", " 0.2956371883523189,\n", " 0.2983577178215843,\n", " 0.2968470019133456,\n", " 0.2871219621341666,\n", " 0.2914710867817894,\n", " 0.299545816780251,\n", " 0.2974270920054099,\n", " 0.2880525879503408,\n", " 0.3045246648971512,\n", " 0.3055483532944175,\n", " 0.3085480705088505,\n", " 0.3091312626874766,\n", " 0.3131081370086299,\n", " 0.3200412993388656,\n", " 0.3386786323512731,\n", " 0.3274863058682599,\n", " 0.3350243749797971,\n", " 0.3451216650850254,\n", " 0.3448890086312446,\n", " 0.3517539250682592,\n", " 0.3594315880514344,\n", " 0.3573128632774341,\n", " 0.3612680229959121,\n", " 0.3629214348623507,\n", " 0.3688774400858662,\n", " 0.3797874767383059,\n", " 0.3807181025544802,\n", " 0.3855356421959114,\n", " 0.3713901297900621,\n", " 0.3711791879379057,\n", " 0.3816735450585254,\n", " 0.3800914811715545,\n", " 0.372832599805395,\n", " 0.3897234583692211,\n", " 0.3870463581044653,\n", " 0.3841397034721753,\n", " 0.3809290444055859,\n", " 0.3826724167679209,\n", " 0.3971436482096937,\n", " 0.3890720202973737,\n", " 0.3758323170187771,\n", " 0.3620838716283192,\n", " 0.3767164115444053,\n", " 0.420316231032959,\n", " 0.3627104930112449,\n", " 0.3587336186900918,\n", " 0.3535221141195158,\n", " 0.3386538156623514,\n", " 0.340422004713608,\n", " 0.3374222874991752,\n", " 0.3409586656006374,\n", " 0.3370717517753805,\n", " 0.3151337992015381,\n", " 0.3283486857912131,\n", " 0.2996854106527297,\n", " 0.3077570385653649,\n", " 0.2934719322866167,\n", " 0.2801887998036155,\n", " 0.2787928610793541,\n", " 0.2797234868955284,\n", " 0.2618306545362705,\n", " 0.2584338703071818,\n", " 0.2401005417283381,\n", " 0.2566656812564507,\n", " 0.2170458381752966,\n", " 0.2225117138023287,\n", " 0.2146727423439996,\n", " 0.2042032019118918,\n", " 0.1957096902961518,\n", " 0.1946642872959687,\n", " 0.1893814347460826,\n", " 0.1795633323853072,\n", " 0.1759587083906238,\n", " 0.1619279731685417,\n", " 0.1566482227047445,\n", " 0.1545543146182998,\n", " 0.1441530200794025,\n", " 0.1480399339049747,\n", " 0.1410819548859223,\n", " 0.1345923908610986,\n", " 0.131242137922766,\n", " 0.1117672416760118,\n", " 0.1070923979927804,\n", " 0.1099059900436632,\n", " 0.1037173283660516,\n", " 0.0966911034537781,\n", " 0.0935983236580431,\n", " 0.090155008137988,\n", " 0.0879432207818449,\n", " 0.0872700681083731,\n", " 0.0796389364157439,\n", " 0.0760777416253599,\n", " 0.0783825915636459,\n", " 0.0718433941615042,\n", " 0.0746818029005731,\n", " 0.0715424918145022,\n", " 0.0659339202285348,\n", " 0.0709375850342516,\n", " 0.0680061137130399,\n", " 0.0674477382233353,\n", " 0.0646775753774876,\n", " 0.0595591333880036,\n", " 0.0638182975397302,\n", " 0.0566990100462595,\n", " 0.0582562572453595,\n", " 0.0569068498121695,\n", " 0.0534635342917993,\n", " 0.058792918132389,\n", " 0.0546051019601301,\n", " 0.0517201619294644,\n", " 0.0512083177303058,\n", " 0.0528120962210026,\n", " 0.0480441899553132,\n", " 0.0477650022109864,\n", " 0.0479976586647672,\n", " 0.0485095028639258,\n", " 0.0506716568432764,\n", " 0.0486490967366146,\n", " 0.0482054984306772,\n", " 0.0458106879964949,\n", " 0.0494153119911784,\n", " 0.0457641567059489,\n", " 0.0443434012932912,\n", " 0.0487886906082527,\n", " 0.0457641567059489,\n", " 0.0446939370170858,\n", " 0.0440890302368352,\n", " 0.0451127186351523,\n", " 0.045578031542714,\n", " 0.0438563737830543,\n", " 0.0452057812162443,\n", " 0.0459968131597298,\n", " 0.0467878451042661,\n", " 0.0476254083382975,\n", " 0.0520458809653881,\n", " 0.0486956280271606,\n", " 0.0511152551492138,\n", " 0.057396979408653,\n", " 0.0587681014445181,\n", " 0.062655015269775,\n", " 0.0627697924535422,\n", " 0.0630955114894659,\n", " 0.0716355543955942,\n", " 0.069377235748905,\n", " 0.0804299683592294,\n", " 0.0750540532270428,\n", " 0.0806378081240886,\n", " 0.0814288400686248,\n", " 0.0926211665505872,\n", " 0.0951555708571431,\n", " 0.1008106737334464,\n", " 0.1004849546978379,\n", " 0.1060221783041275,\n", " 0.1137929038693404,\n", " 0.1170718088283103,\n", " 0.1273800407855902,\n", " 0.134918109896707,\n", " 0.1368010761315206,\n", " 0.1389663321971177,\n", " 0.143759055150573,\n", " 0.1543681894550649,\n", " 0.1548086856747558,\n", " 0.1600915382245894,\n", " 0.1683492913002494,\n", " 0.1693264484072849,\n", " 0.1801930558542693,\n", " 0.1910565612152173,\n", " 0.1990599432344318,\n", " 0.2022240710094665,\n", " 0.2044110416775076,\n", " 0.2145796797623823,\n", " 0.220417805715884,\n", " 0.2292835676581462,\n", " 0.2423123290847965,\n", " 0.2430568297377359,\n", " 0.2676253512850524,\n", " 0.2597615631383272,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " 0.3263013089956296,\n", " 0.3177147347979045,\n", " 0.3199482367567228,\n", " 0.3372113456470186,\n", " 0.3118052608659858,\n", " 0.2972657835305714,\n", " 0.3027099445553486,\n", " 0.2990556871837675,\n", " 0.3085728871977721,\n", " 0.3162970814714933,\n", " 0.3123419217530153,\n", " 0.3270675242512442,\n", " 0.316669331797963,\n", " 0.3210432731345076,\n", " 0.3145754237118336,\n", " 0.3122705737735476,\n", " 0.3171346447065755,\n", " 0.3121557965897804,\n", " 0.3230193019506233,\n", " 0.3264874341588645,\n", " 0.3303960625867965,\n", " 0.3331631233474485,\n", " 0.3401211023655551,\n", " 0.3380954401726469,\n", " 0.3405864152741677,\n", " 0.3558486786594261,\n", " 0.3566862418945084,\n", " 0.3649905262606093,\n", " 0.3672705595099736,\n", " 0.3807646338450262,\n", " 0.3777183853400473,\n", " 0.3844654225070483,\n", " 0.3773213183246559,\n", " 0.38604748639507,\n", " 0.3963309016635332,\n", " 0.3958407720670497,\n", " 0.3948884316482003,\n", " 0.4047530653006986,\n", " 0.3996563379138897,\n", " 0.3927014609809789,\n", " 0.3873255458487922,\n", " 0.3888858951341389,\n", " 0.3844654225070483,\n", " 0.3884422968282014,\n", " 0.3900957086956908,\n", " 0.3893977393332974,\n", " 0.3984248097496626,\n", " 0.3865345139053069,\n", " 0.393864743250934,\n", " 0.3902353025673289,\n", " 0.3900491774040941,\n", " 0.4012632184897823,\n", " 0.3899312981340803,\n", " 0.389909583532456,\n", " 0.3919786749296637,\n", " 0.3737384089326477,\n", " 0.3777866312332685,\n", " 0.3695971240509347,\n", " 0.3577316448944498,\n", " 0.3615254961386147,\n", " 0.3502866383650555,\n", " 0.3369786891932377,\n", " 0.3407260091468566,\n", " 0.3248123076897508,\n", " 0.3308148442038122,\n", " 0.3220886761344489,\n", " 0.3045246648971512,\n", " 0.3037553475552901,\n", " 0.2925661231588387,\n", " 0.2897494290219195,\n", " 0.2669739132137305,\n", " 0.2719527613303155,\n", " 0.2575280611794038,\n", " 0.2694400716266448,\n", " 0.2451259211356792,\n", " 0.2386828884016117,\n", " 0.2399857645442557,\n", " 0.2285142503168105,\n", " 0.2206504621699801,\n", " 0.211530329171367,\n", " 0.2163478688131134,\n", " 0.1938267240614013,\n", " 0.1926169105003642,\n", " 0.1722579197282865,\n", " 0.1756081726665139,\n", " 0.1673038883000978,\n", " 0.1594618147556273,\n", " 0.1485052468130618,\n", " 0.1504812756294928,\n", " 0.1462717448542435,\n", " 0.1316143882492357,\n", " 0.1280997247501333,\n", " 0.1161659997005323,\n", " 0.1092793686607375,\n", " 0.1107435532782201,\n", " 0.1029045818198911,\n", " 0.100298829534603,\n", " 0.0986919489586054,\n", " 0.0949477310915483,\n", " 0.0888738465980191,\n", " 0.0856631875324805,\n", " 0.0818724383745622,\n", " 0.0824525284658911,\n", " 0.0800111867422136,\n", " 0.0748213967732619,\n", " 0.0740551815176474,\n", " 0.0741234274108685,\n", " 0.0711919560907076,\n", " 0.0666784208814742,\n", " 0.0679130511308971,\n", " 0.070633580601003,\n", " 0.0666318895909282,\n", " 0.0634925785048573,\n", " 0.0637934808518593,\n", " 0.0592116997494047,\n", " 0.0598383211323305,\n", " 0.0574652253018741,\n", " 0.0577692297351227,\n", " 0.0552317233430558,\n", " 0.0529982213842375,\n", " 0.0515805680567755,\n", " 0.0522537207312981,\n", " 0.0517666932200104,\n", " 0.0492540035168652,\n", " 0.0490430616647087,\n", " 0.0466482512315772,\n", " 0.0481837838280021,\n", " 0.0460433444513266,\n", " 0.0449731247624635,\n", " 0.0446225890386689,\n", " 0.0448800621803207,\n", " 0.0472066267212818,\n", " 0.0450879019462306,\n", " 0.0430653418395689,\n", " 0.0445543431454477,\n", " 0.0473214039050489,\n", " 0.0480441899553132,\n", " 0.0450661873435555,\n", " 0.0466482512315772,\n", " 0.0449948393651386,\n", " 0.0475075290682838,\n", " 0.0461581216350938,\n", " 0.0469739702675009,\n", " 0.0466017199410312,\n", " 0.0504172857868204,\n", " 0.0492291868279435,\n", " 0.0498340936092449,\n", " 0.054930820995003,\n", " 0.0557218529395392,\n", " 0.0615382642903658,\n", " 0.0672616130601004,\n", " 0.066771483463617,\n", " 0.066610174988253,\n", " 0.0718216795588291,\n", " 0.0790092129465716,\n", " 0.0793349319814445,\n", " 0.0825704077359048,\n", " 0.0830574352471925,\n", " 0.0892460969244889,\n", " 0.0931330107499559,\n", " 0.0935517923671818,\n", " 0.0936665695512642,\n", " 0.1021600811669516,\n", " 0.1075794255032275,\n", " 0.1140938062165526,\n", " 0.121051785235605,\n", " 0.1242841589038187,\n", " 0.1353120748256417,\n", " 0.1406631732686964,\n", " 0.1406414586663365,\n", " 0.1431293316816108,\n", " 0.1540890017102127,\n", " 0.1572748440876702,\n", " 0.1645337254538297,\n", " 0.1725619241615351,\n", " 0.1884973402207064,\n", " 0.1887765279655588,\n", " 0.1907308421795563,\n", " 0.202714200605992,\n", " 0.20245672746351,\n", " 0.2108788911000449,\n", " 0.2174398031041263,\n", " 0.2181160578638445,\n", " 0.2420796726307004,\n", " 0.2555737469654378,\n", " 0.2532006511341408,\n", " 0.2709756042232799,\n", " 0.2773503910642314,\n", " 0.2754891394318829,\n", " 0.2872832706090052,\n", " 0.2968687165157054,\n", " 0.2995923480711122,\n", " 0.3036871016620689,\n", " 0.3029643156116996,\n", " 0.3228114621847134,\n", " 0.3308148442038122,\n", " 0.3235094315471068,\n", " 0.3281160293374322,\n", " 0.3336067216523351,\n", " 0.4831334736252489,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " nan,\n", " 0.3061749746773433,\n", " 0.3150624512220704,\n", " 0.315620826711775,\n", " 0.3056165991876387,\n", " 0.3084084766361615,\n", " 0.3102697282685102,\n", " 0.310455853431745,\n", " 0.3209967418429108,\n", " 0.3132942621718647,\n", " 0.3251845580162205,\n", " 0.3198551741756307,\n", " 0.3398419146212282,\n", " 0.3240678070368114,\n", " 0.320878862572897,\n", " 0.3357223443411397,\n", " 0.3497996108548186,\n", " 0.3523123005579638,\n", " 0.342819917232986,\n", " 0.3531963950835921,\n", " 0.3715049069738293,\n", " 0.3762510986368437,\n", " 0.4132434748300344,\n", " 0.3799984185894117,\n", " 0.3869315809206982,\n", " 0.3976803090967232,\n", " 0.385092043889974,\n", " 0.4013562810708744,\n", " 0.3940973997047149,\n", " 0.383556511293549,\n", " 0.3871859519771543,\n", " 0.3844188912165023,\n", " 0.3733444440035028,\n", " 0.4047065340091017,\n", " 0.3840931721805786,\n", " 0.3828120106406097,\n", " 0.3829764212011694,\n", " 0.3980990907147897,\n", " 0.3894442706238434,\n", " 0.3870928893950114,\n", " 0.3858830758345102,\n", " 0.373226564733489,\n", " 0.3655023704597679,\n", " 0.3660142146589264,\n", " 0.3685982523415393,\n", " 0.3648509323879204,\n", " 0.3583365516747004,\n", " 0.3424011356159703,\n", " 0.3371648143564725,\n", " 0.3341402804541689,\n", " 0.3324651539850552,\n", " 0.3166445151100922,\n", " 0.3140852941153501,\n", " 0.3137378604767513,\n", " 0.3026168819737312,\n", " 0.2942412496280575,\n", " 0.28746939577224,\n", " 0.2875407437514976,\n", " 0.2808619524772974,\n", " 0.2682519726679782,\n", " 0.2719527613303155,\n", " 0.2577607176334999,\n", " 0.2555272156745765,\n", " 0.2454051088805315,\n", " 0.2305864438007903,\n", " 0.2384719465499805,\n", " 0.2330743168160645,\n", " 0.2168814276144216,\n", " 0.213230272328877,\n", " 0.2091355187377101,\n", " 0.2073673296869264,\n", " 0.1849144308283619,\n", " 0.1796563949669246,\n", " 0.180168239165873,\n", " 0.1676978532289273,\n", " 0.1607646908982713,\n", " 0.1590895644291576,\n", " 0.1545543146182998,\n", " 0.1490853369051263,\n", " 0.135823919024485,\n", " 0.1308233563055401,\n", " 0.124727757209546,\n", " 0.1159798745372975,\n", " 0.1105357135126254,\n", " 0.104229172564895,\n", " 0.0988315428310841,\n", " 0.1024392689118039,\n", " 0.0926428811532623,\n", " 0.0900371288679743,\n", " 0.0853374684965568,\n", " 0.0871770055262303,\n", " 0.081081406430026,\n", " 0.0806843394156854,\n", " 0.0768935902577671,\n", " 0.074867928063808,\n", " 0.0768005276756243,\n", " 0.0769618361509882,\n", " 0.0708445224521088,\n", " 0.0694702983299971,\n", " 0.0688188602592005,\n", " 0.0649567631218144,\n", " 0.0630489801989199,\n", " 0.0618391666373679,\n", " 0.0609798888006613,\n", " 0.0571395062659504,\n", " 0.0600709775871621,\n", " 0.0553030713214727,\n", " 0.0540932577609715,\n", " 0.0543941601079735,\n", " 0.0501132813535718,\n", " 0.0516736306389184,\n", " 0.0513013803124487,\n", " 0.0519528183832452,\n", " 0.0506251255527304,\n", " 0.0491144096441764,\n", " 0.0484846861750041,\n", " 0.0492074722263192,\n", " 0.0483699089912369,\n", " 0.0482054984306772,\n", " 0.0488352218998495,\n", " 0.0466482512315772,\n", " 0.0471600954307358,\n", " 0.0459037505786378,\n", " 0.0448552454924498,\n", " 0.0438098424925083,\n", " 0.0442968700027452,\n", " 0.0451344332367766,\n", " 0.0430653418395689,\n", " 0.04448299516598,\n", " 0.0450413706556846,\n", " 0.0468809076853581,\n", " 0.0455997461453892,\n", " 0.0462511842161858,\n", " 0.0460650590529509,\n", " 0.0489282844809415,\n", " 0.0488817531903955,\n", " 0.0522320061286229,\n", " 0.0548594730165861,\n", " 0.056488068194103,\n", " 0.0622579482543836,\n", " 0.0616778581630547,\n", " 0.0632816366527007,\n", " 0.0676803946771162,\n", " 0.0724017696512088,\n", " 0.0753797722629665,\n", " 0.0819189696651082,\n", " 0.0854770623692457,\n", " 0.0866186300365257,\n", " 0.0945041327858211,\n", " 0.0957139463468477,\n", " 0.0985523550862318,\n", " 0.1017412995496206,\n", " 0.1124652110381951,\n", " 0.117862840772111,\n", " 0.1311490753411486,\n", " 0.1263780669901583,\n", " 0.1308233563055401,\n", " 0.1472489019612791,\n", " 0.1404553335031017,\n", " 0.1513901868433072,\n", " 0.1542968414758074,\n", " 0.1653712886884916,\n", " 0.1643941315814561,\n", " 0.1737003897433041,\n", " 0.1891953095828476,\n", " 0.1857302794605588,\n", " 0.1983154425814808,\n", " 0.1990134119436231,\n", " 0.2080870136514484,\n", " 0.2161369269614823,\n", " 0.229910189041072,\n", " 0.2250492201945007,\n", " 0.2439409242630489,\n", " 0.269740973973857,\n", " 0.2688103481576828,\n", " 0.2749307639421782,\n", " 0.281817394981868,\n", " 0.2909592425829461,\n", " 0.2947530938269008,\n", " 0.3168089256706519,\n", " 0.3096648214882596,\n", " 0.3284634629749802,\n", " 0.3318137159132077,\n", " 0.3270675242512442,\n", " 0.328556525557123,\n", " 0.3415635723808881,\n", " 0.3453077902482603,\n", " 0.3450068879012583,\n", " 0.3530568012109032,\n", " 0.3456800405747301,\n", " 0.3497065482726758,\n", " 0.3501005132018206,\n", " 0.3722276930240936,\n", " 0.3775322601768125,\n", " 0.3746225034582759,\n", " 0.3696436553414808,\n", " 0.3641994943171238,\n", " 0.3670161884535176,\n", " 0.3723207556062365,\n", " 0.3680398768518347,\n", " 0.3680398768518347,\n", " ...]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["with open('raw_data/ts_nan.json', 'r') as f:\n", "    data = json.load(f) \n", "\n", "data['ts']['value']"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}