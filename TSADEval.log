(2025-08-01 16:47:28,538) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:47:58,825) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:47:58,825) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:17,943) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:17,943) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:17,943) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:17,946) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:17,946) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:17,946) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:26,589) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:26,589) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:26,589) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:26,589) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:26,593) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:26,593) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:26,593) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:26,593) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:35,012) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:48:35,015) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:48:43,656) [INFO]: Run Experiments. Method[CosAIServiceAlgo], Schema[naive].
(2025-08-01 16:51:31,585) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:51:31,587) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:55:04,245) [INFO]: Run Experiments. Method[CosAIServiceAlgo], Schema[naive].
(2025-08-01 16:55:04,245) [INFO]: Use Function Parameters.
(2025-08-01 16:55:04,246) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 16:55:04,318) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 16:55:34,923) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 16:55:34,923) [INFO]: Use Function Parameters.
(2025-08-01 16:55:34,924) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 16:55:34,995) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 16:56:22,016) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:56:22,016) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 16:56:22,019) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:56:22,019) [INFO]: Dataset Directory has been loaded.
(2025-08-01 16:56:22,032) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 16:56:22,032) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 16:56:22,045) [INFO]: Use Function Parameters.
(2025-08-01 16:56:22,045) [INFO]: Use Function Parameters.
(2025-08-01 16:56:22,046) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 16:56:22,046) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 16:56:22,112) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 16:56:22,112) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:01:13,037) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:01:13,038) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:01:13,047) [INFO]: Run Experiments. Method[CosAIServiceAlgo], Schema[naive].
(2025-08-01 17:01:13,047) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:01:13,050) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:01:13,120) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:01:28,343) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:01:28,344) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:01:28,359) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:01:28,361) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:01:28,363) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:01:28,382) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:01:37,832) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:01:37,833) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:01:37,840) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:01:37,841) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:01:37,841) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:01:37,861) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:05:55,672) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:05:55,673) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:05:55,681) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:05:55,682) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:05:55,682) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:05:55,699) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:05:55,701) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 17:05:55,702) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 17:05:55,702) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 17:05:55,703) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 17:05:55,704) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 17:05:55,705) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 17:05:55,705) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 17:05:55,706) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 17:05:55,707) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 17:05:55,707) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 17:05:55,708) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 17:05:55,708) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 17:05:55,709) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 17:05:55,709) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 17:05:55,710) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 17:05:55,710) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 17:05:55,711) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 17:05:55,712) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 17:05:55,712) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 17:05:55,713) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 17:05:55,713) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 17:05:55,714) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 17:05:55,714) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 17:05:55,715) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 17:05:55,715) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 17:05:55,716) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 17:05:55,717) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 17:05:55,718) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 17:05:56,445) [INFO]: Register evaluations
(2025-08-01 17:05:56,445) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:05:56,446) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:05:56,464) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 17:05:56,465) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 17:06:02,727) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:06:02,727) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:06:02,744) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 17:14:03,420) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:14:03,420) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:14:03,422) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:14:03,422) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:14:03,432) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:03,432) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:03,433) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:14:03,433) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:14:03,434) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:03,434) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:03,510) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:14:03,510) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:14:03,512) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 17:14:03,512) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 17:14:03,513) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 17:14:03,513) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 17:14:03,515) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 17:14:03,515) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 17:14:03,517) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 17:14:03,517) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 17:14:03,519) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 17:14:03,519) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 17:14:03,521) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 17:14:03,521) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 17:14:03,524) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 17:14:03,524) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 17:14:03,526) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 17:14:03,526) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 17:14:03,528) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 17:14:03,528) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 17:14:03,529) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 17:14:03,529) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 17:14:03,531) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 17:14:03,531) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 17:14:03,534) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 17:14:03,534) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 17:14:03,535) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 17:14:03,535) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 17:14:03,539) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 17:14:03,539) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 17:14:03,543) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 17:14:03,543) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 17:14:03,544) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 17:14:03,544) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 17:14:03,549) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 17:14:03,549) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 17:14:03,550) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 17:14:03,550) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 17:14:03,553) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 17:14:03,553) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 17:14:03,556) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 17:14:03,556) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 17:14:03,560) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 17:14:03,560) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 17:14:03,561) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 17:14:03,561) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 17:14:03,562) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 17:14:03,562) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 17:14:03,564) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 17:14:03,564) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 17:14:03,566) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 17:14:03,566) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 17:14:03,568) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 17:14:03,568) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 17:14:03,570) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 17:14:03,570) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 17:14:03,574) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 17:14:03,574) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 17:14:03,588) [INFO]: Register evaluations
(2025-08-01 17:14:03,588) [INFO]: Register evaluations
(2025-08-01 17:14:03,588) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:03,588) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:03,589) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:03,589) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:03,611) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 17:14:03,611) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 17:14:03,611) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 17:14:03,611) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 17:14:10,165) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:10,165) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:10,166) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:10,166) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:10,186) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 17:14:10,186) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 17:14:52,536) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:14:52,537) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:14:52,544) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:52,548) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:14:52,550) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:52,621) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:14:52,623) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 17:14:52,624) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 17:14:52,625) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 17:14:52,626) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 17:14:52,627) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 17:14:52,628) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 17:14:52,629) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 17:14:52,630) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 17:14:52,632) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 17:14:52,633) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 17:14:52,634) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 17:14:52,635) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 17:14:52,636) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 17:14:52,637) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 17:14:52,638) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 17:14:52,639) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 17:14:52,641) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 17:14:52,641) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 17:14:52,642) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 17:14:52,643) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 17:14:52,644) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 17:14:52,645) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 17:14:52,646) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 17:14:52,646) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 17:14:52,648) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 17:14:52,649) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 17:14:52,651) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 17:14:52,653) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 17:14:53,008) [INFO]: Register evaluations
(2025-08-01 17:14:53,008) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:53,008) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:53,025) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 17:14:53,025) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 17:14:59,432) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:14:59,433) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-01 17:14:59,448) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 17:29:30,459) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 17:29:30,461) [INFO]: Dataset Directory has been loaded.
(2025-08-01 17:29:30,473) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:29:30,475) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 17:29:30,476) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 17:29:30,881) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 17:29:30,882) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 17:29:30,884) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 17:29:30,885) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 17:29:30,887) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 17:29:30,889) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 17:29:30,890) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 17:29:30,891) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 17:29:30,893) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 17:29:30,894) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 17:29:30,896) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 17:29:30,897) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 17:29:30,898) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 17:29:30,899) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 17:29:30,900) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 17:29:30,901) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 17:29:30,902) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 17:29:30,904) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 17:29:30,906) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 17:29:30,906) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 17:29:30,908) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 17:29:30,908) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 17:29:30,910) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 17:29:30,910) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 17:29:30,911) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 17:29:30,912) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 17:29:30,916) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 17:29:30,918) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 17:29:30,919) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 17:29:30,922) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-01 17:29:30,923) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-01 17:29:30,923) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-01 17:29:30,924) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-01 17:29:30,924) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-01 17:29:30,925) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-01 17:29:30,926) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-01 17:29:30,926) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-01 17:29:30,927) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-01 17:29:30,928) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-01 17:29:30,928) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-01 17:29:30,929) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-01 17:29:30,929) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-01 17:29:30,930) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-01 17:29:30,930) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-01 17:29:30,931) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-01 17:29:30,931) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-01 17:29:30,932) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-01 17:29:30,932) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-01 17:29:30,933) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-01 17:29:30,934) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-01 17:29:30,934) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-01 17:29:30,935) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-01 17:29:30,935) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-01 17:29:30,936) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-01 17:29:30,937) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-01 17:29:30,937) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-01 17:29:30,938) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-01 17:29:30,939) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-01 17:29:30,940) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-01 17:29:30,940) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-01 17:29:30,941) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-01 17:29:30,943) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-01 17:29:30,944) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-01 17:29:30,944) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-01 17:29:30,945) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-01 17:29:30,945) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-01 17:29:30,946) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-01 17:29:30,947) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-01 17:29:30,947) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-01 17:29:30,948) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-01 17:29:30,950) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-01 17:29:30,951) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-01 17:29:30,951) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-01 17:29:30,952) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-01 17:29:30,952) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-01 17:29:30,953) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-01 17:29:30,953) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-01 17:29:30,954) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-01 17:29:30,955) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-01 17:29:30,955) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-01 17:29:30,956) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-01 17:29:30,956) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-01 17:29:30,957) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-01 17:29:30,958) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-01 17:29:30,959) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-01 17:29:30,959) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-01 17:29:30,960) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-01 17:29:30,960) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-01 17:29:30,961) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-01 17:29:30,962) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-01 17:29:30,963) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-01 17:29:30,964) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-01 17:29:30,964) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-01 17:29:30,965) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-01 17:29:30,967) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-01 17:29:30,968) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-01 17:29:30,968) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-01 17:29:30,969) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-01 17:29:30,970) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-01 17:29:30,970) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-01 17:29:30,971) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-01 17:29:30,972) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-01 17:29:30,972) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-01 17:29:30,973) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-01 17:29:30,973) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-01 17:29:30,974) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-01 17:29:30,974) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-01 17:29:30,975) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-01 17:29:30,976) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-01 17:29:30,976) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-01 17:29:30,977) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-01 17:29:30,977) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-01 17:29:30,978) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-01 17:29:30,980) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-01 17:29:30,980) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-01 17:29:30,981) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-01 17:29:30,981) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-01 17:29:30,982) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-01 17:29:30,983) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-01 17:29:30,983) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-01 17:29:30,984) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-01 17:29:30,985) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-01 17:29:30,985) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-01 17:29:30,986) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-01 17:29:30,986) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-01 17:29:30,987) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-01 17:29:30,988) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-01 17:29:30,988) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-01 17:29:30,988) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-01 17:29:30,989) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-01 17:29:30,990) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-01 17:29:30,990) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-01 17:29:30,991) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-01 17:29:30,991) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-01 17:29:30,992) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-01 17:29:30,993) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-01 17:29:30,994) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-01 17:29:30,995) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-01 17:29:30,995) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-01 17:29:30,996) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-01 17:29:30,996) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-01 17:29:30,997) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-01 17:29:30,998) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-01 17:29:30,998) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-01 17:29:30,999) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-01 17:29:30,999) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-01 17:29:31,000) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-01 17:29:31,000) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-01 17:29:31,001) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-01 17:29:31,002) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-01 17:29:31,003) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-01 17:29:31,003) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-01 17:29:31,003) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-01 17:29:31,005) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-01 17:29:31,005) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-01 17:29:31,006) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-01 17:29:31,007) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-01 17:29:31,007) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-01 17:29:31,008) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-01 17:29:31,009) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-01 17:29:31,009) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-01 17:29:31,010) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-01 17:29:31,011) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-01 17:29:31,011) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-01 17:29:31,012) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-01 17:29:31,012) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-01 17:29:31,013) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-01 17:29:31,013) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-01 17:29:31,015) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-01 17:29:31,016) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-01 17:29:31,016) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-01 17:29:31,017) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-01 17:29:31,017) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-01 17:29:31,018) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-01 17:29:31,019) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-01 17:29:31,019) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-01 17:29:31,020) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-01 17:29:31,020) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-01 17:29:31,021) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-01 17:29:31,022) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-01 17:29:31,022) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-01 17:29:31,023) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-01 17:29:31,024) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-01 17:29:31,024) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-01 17:29:31,025) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-01 17:29:31,027) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-01 17:29:31,029) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-01 17:29:31,029) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-01 17:29:31,030) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-01 17:29:31,031) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-01 17:29:31,031) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-01 17:29:31,032) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-01 17:29:31,033) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-01 17:29:31,033) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-01 17:29:31,034) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-01 17:29:31,034) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-01 17:29:31,034) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-01 17:29:31,036) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-01 17:29:31,036) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-01 17:29:31,037) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-01 17:29:31,037) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-01 17:29:31,038) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-01 17:29:31,038) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-01 17:29:31,039) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-01 17:29:31,039) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-01 17:29:31,040) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-01 17:29:31,041) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-01 17:29:31,041) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-01 17:29:31,042) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-01 17:29:31,042) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-01 17:29:31,043) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-01 17:29:31,043) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-01 17:29:31,044) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-01 17:29:31,045) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-01 17:29:31,045) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-01 17:29:31,046) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-01 17:29:31,048) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-01 17:29:31,048) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-01 17:29:31,049) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-01 17:29:31,049) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-01 17:29:31,050) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-01 17:29:31,050) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-01 17:29:31,051) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-01 17:29:31,052) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-01 17:29:31,052) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-01 17:29:31,053) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-01 17:29:31,053) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-01 17:29:31,054) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-01 17:29:31,055) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-01 17:29:31,055) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-01 17:29:31,056) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-01 17:29:31,057) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-01 17:29:31,057) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-01 17:29:31,058) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-01 17:29:31,058) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-01 17:29:31,059) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-01 17:29:31,060) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-01 17:29:31,060) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-01 17:29:31,061) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-01 17:29:31,428) [INFO]: Register evaluations
(2025-08-01 17:29:31,428) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:29:31,428) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 17:29:31,512) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 17:29:31,512) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 17:29:37,849) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-01 17:29:37,849) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-01 17:29:37,954) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:38,507) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:38,650) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:38,768) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:38,877) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:38,912) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,019) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,198) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,198) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,305) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,305) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,414) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,560) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,813) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,813) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,814) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,815) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,815) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:39,928) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,076) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,222) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,223) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,500) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,500) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,610) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,724) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,829) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:40,830) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,018) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,126) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,392) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,392) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,393) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,393) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,433) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,433) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,659) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:41,876) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:42,104) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:42,370) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:42,677) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:42,952) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:42,952) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,137) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,138) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,628) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,781) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,782) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,859) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-01 17:29:43,870) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 17:29:43,870) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 17:29:43,931) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 17:29:52,960) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-01 18:42:00,662) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 18:42:00,663) [INFO]: Dataset Directory has been loaded.
(2025-08-01 18:42:00,670) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:42:00,670) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 18:42:00,671) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:42:01,090) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 18:42:01,092) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 18:42:01,095) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 18:42:01,099) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 18:42:01,100) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 18:42:01,101) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 18:42:01,102) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 18:42:01,104) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 18:42:01,105) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 18:42:01,107) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 18:42:01,109) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 18:42:01,111) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 18:42:01,113) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 18:42:01,113) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 18:42:01,114) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 18:42:01,115) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 18:42:01,118) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 18:42:01,120) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 18:42:01,120) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 18:42:01,121) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 18:42:01,123) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 18:42:01,124) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 18:42:01,125) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 18:42:01,126) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 18:42:01,127) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 18:42:01,129) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 18:42:01,130) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 18:42:01,132) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 18:42:01,135) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 18:42:01,137) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-01 18:42:01,139) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-01 18:42:01,140) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-01 18:42:01,140) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-01 18:42:01,141) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-01 18:42:01,142) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-01 18:42:01,143) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-01 18:42:01,144) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-01 18:42:01,144) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-01 18:42:01,146) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-01 18:42:01,147) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-01 18:42:01,148) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-01 18:42:01,148) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-01 18:42:01,149) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-01 18:42:01,149) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-01 18:42:01,150) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-01 18:42:01,152) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-01 18:42:01,152) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-01 18:42:01,153) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-01 18:42:01,154) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-01 18:42:01,155) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-01 18:42:01,155) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-01 18:42:01,156) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-01 18:42:01,156) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-01 18:42:01,157) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-01 18:42:01,160) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-01 18:42:01,160) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-01 18:42:01,161) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-01 18:42:01,162) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-01 18:42:01,162) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-01 18:42:01,163) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-01 18:42:01,164) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-01 18:42:01,165) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-01 18:42:01,166) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-01 18:42:01,167) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-01 18:42:01,168) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-01 18:42:01,168) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-01 18:42:01,168) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-01 18:42:01,169) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-01 18:42:01,170) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-01 18:42:01,170) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-01 18:42:01,171) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-01 18:42:01,172) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-01 18:42:01,172) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-01 18:42:01,173) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-01 18:42:01,173) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-01 18:42:01,174) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-01 18:42:01,175) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-01 18:42:01,175) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-01 18:42:01,177) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-01 18:42:01,178) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-01 18:42:01,178) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-01 18:42:01,179) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-01 18:42:01,179) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-01 18:42:01,180) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-01 18:42:01,182) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-01 18:42:01,183) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-01 18:42:01,183) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-01 18:42:01,184) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-01 18:42:01,185) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-01 18:42:01,185) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-01 18:42:01,186) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-01 18:42:01,186) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-01 18:42:01,187) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-01 18:42:01,188) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-01 18:42:01,189) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-01 18:42:01,189) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-01 18:42:01,190) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-01 18:42:01,190) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-01 18:42:01,191) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-01 18:42:01,192) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-01 18:42:01,192) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-01 18:42:01,193) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-01 18:42:01,194) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-01 18:42:01,194) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-01 18:42:01,195) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-01 18:42:01,195) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-01 18:42:01,196) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-01 18:42:01,199) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-01 18:42:01,200) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-01 18:42:01,200) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-01 18:42:01,201) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-01 18:42:01,201) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-01 18:42:01,202) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-01 18:42:01,204) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-01 18:42:01,205) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-01 18:42:01,205) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-01 18:42:01,206) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-01 18:42:01,206) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-01 18:42:01,207) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-01 18:42:01,207) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-01 18:42:01,208) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-01 18:42:01,209) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-01 18:42:01,210) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-01 18:42:01,210) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-01 18:42:01,211) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-01 18:42:01,211) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-01 18:42:01,212) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-01 18:42:01,212) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-01 18:42:01,213) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-01 18:42:01,214) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-01 18:42:01,214) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-01 18:42:01,215) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-01 18:42:01,216) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-01 18:42:01,217) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-01 18:42:01,217) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-01 18:42:01,218) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-01 18:42:01,219) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-01 18:42:01,220) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-01 18:42:01,221) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-01 18:42:01,221) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-01 18:42:01,222) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-01 18:42:01,223) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-01 18:42:01,223) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-01 18:42:01,224) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-01 18:42:01,224) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-01 18:42:01,225) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-01 18:42:01,226) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-01 18:42:01,226) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-01 18:42:01,227) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-01 18:42:01,228) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-01 18:42:01,228) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-01 18:42:01,229) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-01 18:42:01,230) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-01 18:42:01,230) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-01 18:42:01,231) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-01 18:42:01,232) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-01 18:42:01,232) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-01 18:42:01,233) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-01 18:42:01,234) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-01 18:42:01,234) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-01 18:42:01,236) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-01 18:42:01,237) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-01 18:42:01,237) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-01 18:42:01,238) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-01 18:42:01,239) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-01 18:42:01,239) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-01 18:42:01,240) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-01 18:42:01,242) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-01 18:42:01,242) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-01 18:42:01,243) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-01 18:42:01,245) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-01 18:42:01,245) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-01 18:42:01,246) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-01 18:42:01,247) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-01 18:42:01,247) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-01 18:42:01,248) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-01 18:42:01,248) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-01 18:42:01,249) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-01 18:42:01,250) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-01 18:42:01,252) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-01 18:42:01,253) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-01 18:42:01,253) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-01 18:42:01,254) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-01 18:42:01,254) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-01 18:42:01,255) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-01 18:42:01,256) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-01 18:42:01,256) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-01 18:42:01,257) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-01 18:42:01,258) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-01 18:42:01,259) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-01 18:42:01,259) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-01 18:42:01,261) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-01 18:42:01,261) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-01 18:42:01,262) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-01 18:42:01,262) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-01 18:42:01,263) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-01 18:42:01,264) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-01 18:42:01,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-01 18:42:01,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-01 18:42:01,266) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-01 18:42:01,266) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-01 18:42:01,267) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-01 18:42:01,267) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-01 18:42:01,268) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-01 18:42:01,268) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-01 18:42:01,269) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-01 18:42:01,270) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-01 18:42:01,271) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-01 18:42:01,271) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-01 18:42:01,272) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-01 18:42:01,272) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-01 18:42:01,273) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-01 18:42:01,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-01 18:42:01,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-01 18:42:01,276) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-01 18:42:01,276) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-01 18:42:01,277) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-01 18:42:01,278) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-01 18:42:01,280) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-01 18:42:01,281) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-01 18:42:01,281) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-01 18:42:01,282) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-01 18:42:01,283) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-01 18:42:01,283) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-01 18:42:01,284) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-01 18:42:01,285) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-01 18:42:01,285) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-01 18:42:01,286) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-01 18:42:01,286) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-01 18:42:01,287) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-01 18:42:01,287) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-01 18:42:01,289) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-01 18:42:01,289) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-01 18:42:01,290) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-01 18:42:01,290) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-01 18:42:01,291) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-01 18:42:01,292) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-01 18:42:01,292) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-01 18:42:01,293) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-01 18:42:01,673) [INFO]: Register evaluations
(2025-08-01 18:42:01,674) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:42:01,674) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:42:01,751) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 18:42:01,751) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 18:42:08,133) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-01 18:42:08,134) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-01 18:42:08,237) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:08,783) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:08,929) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,038) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,145) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,179) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,288) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,466) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,466) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,576) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,576) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,687) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:09,831) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,095) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,096) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,096) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,097) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,097) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,209) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,357) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,501) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,502) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,764) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,765) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,873) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:10,986) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,092) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,093) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,278) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,385) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,651) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,651) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,652) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,652) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,691) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,691) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:11,915) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:12,133) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:12,375) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:12,639) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:12,941) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:13,210) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:13,211) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:13,395) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:13,395) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:13,883) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:14,035) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:14,036) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:14,113) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:42:14,126) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:42:14,126) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:42:14,191) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 18:42:22,715) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-01 18:44:39,813) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 18:44:39,816) [INFO]: Dataset Directory has been loaded.
(2025-08-01 18:44:39,823) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:44:39,824) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 18:44:39,824) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:44:40,204) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 18:46:37,511) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 18:46:37,512) [INFO]: Dataset Directory has been loaded.
(2025-08-01 18:46:37,520) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:46:37,521) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 18:46:37,521) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:46:37,589) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 18:46:37,592) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 18:46:37,594) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 18:46:37,596) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 18:46:37,598) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 18:46:37,602) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 18:46:37,604) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 18:46:37,606) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 18:46:37,608) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 18:46:37,611) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 18:46:37,612) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 18:46:37,615) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 18:46:37,617) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 18:46:37,617) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 18:46:37,619) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 18:46:37,621) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 18:46:37,623) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 18:46:37,625) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 18:46:37,629) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 18:46:37,630) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 18:46:37,632) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 18:46:37,633) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 18:46:37,635) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 18:46:37,636) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 18:46:37,637) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 18:46:37,639) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 18:46:37,641) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 18:46:37,643) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 18:46:37,646) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 18:46:37,648) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-01 18:46:37,649) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-01 18:46:37,651) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-01 18:46:37,652) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-01 18:46:37,653) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-01 18:46:37,654) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-01 18:46:37,655) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-01 18:46:37,655) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-01 18:46:37,656) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-01 18:46:37,657) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-01 18:46:37,657) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-01 18:46:37,658) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-01 18:46:37,658) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-01 18:46:37,659) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-01 18:46:37,660) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-01 18:46:37,660) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-01 18:46:37,662) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-01 18:46:37,662) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-01 18:46:37,663) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-01 18:46:37,664) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-01 18:46:37,665) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-01 18:46:37,665) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-01 18:46:37,666) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-01 18:46:37,666) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-01 18:46:37,667) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-01 18:46:37,668) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-01 18:46:37,668) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-01 18:46:37,669) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-01 18:46:37,670) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-01 18:46:37,672) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-01 18:46:37,673) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-01 18:46:37,674) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-01 18:46:37,674) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-01 18:46:37,675) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-01 18:46:37,676) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-01 18:46:37,676) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-01 18:46:37,677) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-01 18:46:37,678) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-01 18:46:37,679) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-01 18:46:37,680) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-01 18:46:37,680) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-01 18:46:37,681) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-01 18:46:37,682) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-01 18:46:37,682) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-01 18:46:37,683) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-01 18:46:37,683) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-01 18:46:37,684) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-01 18:46:37,685) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-01 18:46:37,685) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-01 18:46:37,686) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-01 18:46:37,687) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-01 18:46:37,687) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-01 18:46:37,688) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-01 18:46:37,689) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-01 18:46:37,689) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-01 18:46:37,690) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-01 18:46:37,691) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-01 18:46:37,692) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-01 18:46:37,692) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-01 18:46:37,694) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-01 18:46:37,695) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-01 18:46:37,696) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-01 18:46:37,696) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-01 18:46:37,697) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-01 18:46:37,698) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-01 18:46:37,699) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-01 18:46:37,699) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-01 18:46:37,700) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-01 18:46:37,701) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-01 18:46:37,701) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-01 18:46:37,702) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-01 18:46:37,703) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-01 18:46:37,704) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-01 18:46:37,704) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-01 18:46:37,705) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-01 18:46:37,706) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-01 18:46:37,707) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-01 18:46:37,707) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-01 18:46:37,709) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-01 18:46:37,711) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-01 18:46:37,711) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-01 18:46:37,712) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-01 18:46:37,714) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-01 18:46:37,715) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-01 18:46:37,715) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-01 18:46:37,716) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-01 18:46:37,716) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-01 18:46:37,717) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-01 18:46:37,718) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-01 18:46:37,719) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-01 18:46:37,720) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-01 18:46:37,720) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-01 18:46:37,721) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-01 18:46:37,722) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-01 18:46:37,723) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-01 18:46:37,723) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-01 18:46:37,724) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-01 18:46:37,725) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-01 18:46:37,725) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-01 18:46:37,726) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-01 18:46:37,727) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-01 18:46:37,728) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-01 18:46:37,728) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-01 18:46:37,729) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-01 18:46:37,730) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-01 18:46:37,731) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-01 18:46:37,732) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-01 18:46:37,734) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-01 18:46:37,735) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-01 18:46:37,736) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-01 18:46:37,737) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-01 18:46:37,737) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-01 18:46:37,740) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-01 18:46:37,741) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-01 18:46:37,742) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-01 18:46:37,743) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-01 18:46:37,745) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-01 18:46:37,745) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-01 18:46:37,746) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-01 18:46:37,747) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-01 18:46:37,748) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-01 18:46:37,756) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-01 18:46:37,766) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-01 18:46:37,771) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-01 18:46:37,771) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-01 18:46:37,772) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-01 18:46:37,773) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-01 18:46:37,773) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-01 18:46:37,774) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-01 18:46:37,774) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-01 18:46:37,775) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-01 18:46:37,776) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-01 18:46:37,776) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-01 18:46:37,777) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-01 18:46:37,778) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-01 18:46:37,778) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-01 18:46:37,779) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-01 18:46:37,779) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-01 18:46:37,780) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-01 18:46:37,780) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-01 18:46:37,781) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-01 18:46:37,782) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-01 18:46:37,782) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-01 18:46:37,783) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-01 18:46:37,783) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-01 18:46:37,784) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-01 18:46:37,785) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-01 18:46:37,785) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-01 18:46:37,786) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-01 18:46:37,787) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-01 18:46:37,787) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-01 18:46:37,790) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-01 18:46:37,790) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-01 18:46:37,791) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-01 18:46:37,792) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-01 18:46:37,792) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-01 18:46:37,793) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-01 18:46:37,793) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-01 18:46:37,794) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-01 18:46:37,795) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-01 18:46:37,795) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-01 18:46:37,796) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-01 18:46:37,797) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-01 18:46:37,797) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-01 18:46:37,798) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-01 18:46:37,799) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-01 18:46:37,800) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-01 18:46:37,800) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-01 18:46:37,801) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-01 18:46:37,802) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-01 18:46:37,802) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-01 18:46:37,803) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-01 18:46:37,804) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-01 18:46:37,806) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-01 18:46:37,807) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-01 18:46:37,807) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-01 18:46:37,808) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-01 18:46:37,809) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-01 18:46:37,810) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-01 18:46:37,811) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-01 18:46:37,811) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-01 18:46:37,812) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-01 18:46:37,813) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-01 18:46:37,813) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-01 18:46:37,814) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-01 18:46:37,815) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-01 18:46:37,816) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-01 18:46:37,816) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-01 18:46:37,817) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-01 18:46:37,818) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-01 18:46:37,819) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-01 18:46:37,819) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-01 18:46:37,820) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-01 18:46:37,821) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-01 18:46:37,821) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-01 18:46:37,822) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-01 18:46:37,823) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-01 18:46:37,823) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-01 18:46:37,824) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-01 18:46:37,825) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-01 18:46:37,826) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-01 18:46:37,827) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-01 18:46:37,827) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-01 18:46:37,828) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-01 18:46:37,828) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-01 18:46:37,829) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-01 18:46:37,830) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-01 18:46:37,831) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-01 18:46:37,832) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-01 18:46:37,833) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-01 18:46:38,211) [INFO]: Register evaluations
(2025-08-01 18:46:38,211) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:46:38,212) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:46:38,276) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 18:46:38,276) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 18:46:44,701) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-01 18:46:44,701) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-01 18:46:44,806) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:45,366) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:45,515) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:45,625) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:45,735) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:45,771) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:45,883) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,068) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,068) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,181) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,182) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,293) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,450) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,708) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,708) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,709) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,710) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,710) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,829) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:46,980) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,132) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,133) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,406) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,406) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,523) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,638) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,744) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,745) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:47,936) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,053) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,322) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,323) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,323) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,324) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,363) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,364) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,598) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:48,823) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:49,054) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:49,321) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:49,633) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:49,909) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:49,910) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,098) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,098) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,599) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,755) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,755) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,833) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:46:50,844) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:46:50,844) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:46:50,907) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 18:46:59,457) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-01 18:48:57,959) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 18:48:57,960) [INFO]: Dataset Directory has been loaded.
(2025-08-01 18:48:57,967) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:48:57,968) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 18:48:57,969) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:48:58,365) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 18:48:58,367) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 18:48:58,369) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 18:48:58,371) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 18:48:58,372) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 18:48:58,376) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 18:48:58,378) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 18:48:58,380) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 18:48:58,383) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 18:48:58,385) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 18:48:58,386) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 18:48:58,388) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 18:48:58,390) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 18:48:58,393) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 18:48:58,394) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 18:48:58,395) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 18:48:58,399) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 18:48:58,401) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 18:48:58,402) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 18:48:58,403) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 18:48:58,405) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 18:48:58,407) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 18:48:58,409) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 18:48:58,409) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 18:48:58,410) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 18:48:58,411) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 18:48:58,413) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 18:48:58,415) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 18:48:58,418) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 18:48:58,420) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-01 18:48:58,421) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-01 18:48:58,422) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-01 18:48:58,423) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-01 18:48:58,424) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-01 18:48:58,425) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-01 18:48:58,426) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-01 18:48:58,426) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-01 18:48:58,427) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-01 18:48:58,429) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-01 18:48:58,429) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-01 18:48:58,430) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-01 18:48:58,431) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-01 18:48:58,432) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-01 18:48:58,432) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-01 18:48:58,435) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-01 18:48:58,436) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-01 18:48:58,436) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-01 18:48:58,437) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-01 18:48:58,438) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-01 18:48:58,439) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-01 18:48:58,439) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-01 18:48:58,440) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-01 18:48:58,441) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-01 18:48:58,442) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-01 18:48:58,442) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-01 18:48:58,443) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-01 18:48:58,444) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-01 18:48:58,445) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-01 18:48:58,445) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-01 18:48:58,446) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-01 18:48:58,447) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-01 18:48:58,448) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-01 18:48:58,448) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-01 18:48:58,449) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-01 18:48:58,449) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-01 18:48:58,450) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-01 18:48:58,450) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-01 18:48:58,451) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-01 18:48:58,453) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-01 18:48:58,454) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-01 18:48:58,454) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-01 18:48:58,455) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-01 18:48:58,455) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-01 18:48:58,456) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-01 18:48:58,456) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-01 18:48:58,457) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-01 18:48:58,458) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-01 18:48:58,458) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-01 18:48:58,459) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-01 18:48:58,459) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-01 18:48:58,460) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-01 18:48:58,460) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-01 18:48:58,461) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-01 18:48:58,461) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-01 18:48:58,462) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-01 18:48:58,463) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-01 18:48:58,463) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-01 18:48:58,464) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-01 18:48:58,465) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-01 18:48:58,465) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-01 18:48:58,466) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-01 18:48:58,466) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-01 18:48:58,467) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-01 18:48:58,468) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-01 18:48:58,468) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-01 18:48:58,469) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-01 18:48:58,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-01 18:48:58,472) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-01 18:48:58,472) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-01 18:48:58,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-01 18:48:58,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-01 18:48:58,474) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-01 18:48:58,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-01 18:48:58,476) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-01 18:48:58,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-01 18:48:58,478) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-01 18:48:58,478) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-01 18:48:58,479) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-01 18:48:58,479) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-01 18:48:58,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-01 18:48:58,481) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-01 18:48:58,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-01 18:48:58,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-01 18:48:58,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-01 18:48:58,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-01 18:48:58,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-01 18:48:58,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-01 18:48:58,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-01 18:48:58,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-01 18:48:58,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-01 18:48:58,487) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-01 18:48:58,487) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-01 18:48:58,488) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-01 18:48:58,488) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-01 18:48:58,489) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-01 18:48:58,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-01 18:48:58,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-01 18:48:58,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-01 18:48:58,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-01 18:48:58,494) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-01 18:48:58,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-01 18:48:58,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-01 18:48:58,496) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-01 18:48:58,497) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-01 18:48:58,497) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-01 18:48:58,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-01 18:48:58,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-01 18:48:58,499) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-01 18:48:58,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-01 18:48:58,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-01 18:48:58,501) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-01 18:48:58,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-01 18:48:58,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-01 18:48:58,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-01 18:48:58,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-01 18:48:58,504) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-01 18:48:58,506) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-01 18:48:58,507) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-01 18:48:58,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-01 18:48:58,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-01 18:48:58,511) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-01 18:48:58,511) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-01 18:48:58,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-01 18:48:58,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-01 18:48:58,513) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-01 18:48:58,514) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-01 18:48:58,514) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-01 18:48:58,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-01 18:48:58,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-01 18:48:58,516) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-01 18:48:58,516) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-01 18:48:58,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-01 18:48:58,519) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-01 18:48:58,520) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-01 18:48:58,521) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-01 18:48:58,521) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-01 18:48:58,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-01 18:48:58,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-01 18:48:58,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-01 18:48:58,524) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-01 18:48:58,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-01 18:48:58,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-01 18:48:58,526) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-01 18:48:58,526) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-01 18:48:58,527) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-01 18:48:58,528) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-01 18:48:58,528) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-01 18:48:58,529) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-01 18:48:58,529) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-01 18:48:58,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-01 18:48:58,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-01 18:48:58,531) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-01 18:48:58,532) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-01 18:48:58,533) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-01 18:48:58,534) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-01 18:48:58,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-01 18:48:58,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-01 18:48:58,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-01 18:48:58,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-01 18:48:58,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-01 18:48:58,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-01 18:48:58,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-01 18:48:58,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-01 18:48:58,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-01 18:48:58,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-01 18:48:58,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-01 18:48:58,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-01 18:48:58,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-01 18:48:58,544) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-01 18:48:58,544) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-01 18:48:58,545) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-01 18:48:58,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-01 18:48:58,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-01 18:48:58,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-01 18:48:58,548) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-01 18:48:58,549) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-01 18:48:58,550) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-01 18:48:58,551) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-01 18:48:58,553) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-01 18:48:58,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-01 18:48:58,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-01 18:48:58,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-01 18:48:58,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-01 18:48:58,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-01 18:48:58,557) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-01 18:48:58,557) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-01 18:48:58,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-01 18:48:58,559) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-01 18:48:58,559) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-01 18:48:58,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-01 18:48:58,561) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-01 18:48:58,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-01 18:48:58,563) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-01 18:48:58,563) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-01 18:48:58,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-01 18:48:58,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-01 18:48:58,566) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-01 18:48:58,566) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-01 18:48:58,567) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-01 18:48:58,568) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-01 18:48:58,569) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-01 18:48:58,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-01 18:48:58,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-01 18:48:58,571) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-01 18:48:58,572) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-01 18:48:58,572) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-01 18:48:58,573) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-01 18:48:58,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-01 18:48:58,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-01 18:48:58,952) [INFO]: Register evaluations
(2025-08-01 18:48:58,954) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:48:58,955) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:48:59,024) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 18:48:59,025) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 18:49:05,642) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-01 18:49:05,643) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-01 18:49:05,754) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:06,320) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:06,469) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:06,580) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:06,693) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:06,730) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:06,841) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,026) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,027) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,141) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,141) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,263) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,426) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,695) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,695) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,696) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,696) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,697) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,815) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:07,968) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,125) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,126) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,413) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,414) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,531) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,650) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,761) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,762) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:08,952) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,069) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,357) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,358) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,358) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,359) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,399) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,400) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,633) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:09,858) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:10,102) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:10,388) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:10,714) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:10,994) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:10,995) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,186) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,187) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,688) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,845) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,845) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,931) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:49:11,946) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:49:11,946) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:49:12,014) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 18:49:20,900) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-01 18:52:21,810) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 18:52:21,811) [INFO]: Dataset Directory has been loaded.
(2025-08-01 18:52:21,818) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:52:21,819) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 18:52:21,819) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:52:22,209) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 18:52:22,211) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 18:52:22,212) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 18:52:22,213) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 18:52:22,215) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 18:52:22,218) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 18:52:22,219) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 18:52:22,220) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 18:52:22,223) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 18:52:22,225) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 18:52:22,225) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 18:52:22,227) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 18:52:22,231) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 18:52:22,231) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 18:52:22,232) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 18:52:22,233) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 18:52:22,234) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 18:52:22,237) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 18:52:22,238) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 18:52:22,239) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 18:52:22,240) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 18:52:22,242) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 18:52:22,244) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 18:52:22,244) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 18:52:22,245) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 18:52:22,246) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 18:52:22,249) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 18:52:22,250) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 18:52:22,251) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 18:52:22,253) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-01 18:52:22,257) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-01 18:52:22,257) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-01 18:52:22,258) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-01 18:52:22,259) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-01 18:52:22,261) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-01 18:52:22,262) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-01 18:52:22,264) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-01 18:52:22,264) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-01 18:52:22,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-01 18:52:22,266) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-01 18:52:22,266) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-01 18:52:22,267) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-01 18:52:22,268) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-01 18:52:22,269) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-01 18:52:22,269) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-01 18:52:22,270) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-01 18:52:22,271) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-01 18:52:22,271) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-01 18:52:22,272) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-01 18:52:22,273) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-01 18:52:22,274) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-01 18:52:22,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-01 18:52:22,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-01 18:52:22,276) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-01 18:52:22,277) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-01 18:52:22,277) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-01 18:52:22,278) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-01 18:52:22,279) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-01 18:52:22,279) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-01 18:52:22,281) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-01 18:52:22,282) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-01 18:52:22,283) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-01 18:52:22,283) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-01 18:52:22,284) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-01 18:52:22,285) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-01 18:52:22,285) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-01 18:52:22,287) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-01 18:52:22,288) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-01 18:52:22,289) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-01 18:52:22,290) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-01 18:52:22,290) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-01 18:52:22,291) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-01 18:52:22,292) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-01 18:52:22,292) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-01 18:52:22,293) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-01 18:52:22,294) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-01 18:52:22,295) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-01 18:52:22,295) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-01 18:52:22,296) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-01 18:52:22,297) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-01 18:52:22,297) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-01 18:52:22,298) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-01 18:52:22,298) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-01 18:52:22,299) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-01 18:52:22,299) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-01 18:52:22,300) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-01 18:52:22,300) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-01 18:52:22,301) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-01 18:52:22,301) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-01 18:52:22,305) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-01 18:52:22,305) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-01 18:52:22,306) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-01 18:52:22,306) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-01 18:52:22,307) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-01 18:52:22,307) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-01 18:52:22,308) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-01 18:52:22,308) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-01 18:52:22,308) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-01 18:52:22,309) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-01 18:52:22,310) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-01 18:52:22,310) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-01 18:52:22,310) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-01 18:52:22,311) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-01 18:52:22,311) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-01 18:52:22,312) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-01 18:52:22,312) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-01 18:52:22,314) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-01 18:52:22,315) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-01 18:52:22,315) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-01 18:52:22,316) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-01 18:52:22,316) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-01 18:52:22,317) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-01 18:52:22,318) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-01 18:52:22,318) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-01 18:52:22,319) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-01 18:52:22,320) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-01 18:52:22,320) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-01 18:52:22,321) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-01 18:52:22,322) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-01 18:52:22,322) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-01 18:52:22,323) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-01 18:52:22,323) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-01 18:52:22,324) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-01 18:52:22,324) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-01 18:52:22,325) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-01 18:52:22,325) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-01 18:52:22,326) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-01 18:52:22,326) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-01 18:52:22,327) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-01 18:52:22,327) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-01 18:52:22,327) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-01 18:52:22,328) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-01 18:52:22,328) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-01 18:52:22,329) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-01 18:52:22,329) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-01 18:52:22,330) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-01 18:52:22,330) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-01 18:52:22,332) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-01 18:52:22,333) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-01 18:52:22,333) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-01 18:52:22,334) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-01 18:52:22,334) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-01 18:52:22,334) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-01 18:52:22,335) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-01 18:52:22,336) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-01 18:52:22,337) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-01 18:52:22,337) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-01 18:52:22,338) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-01 18:52:22,338) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-01 18:52:22,339) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-01 18:52:22,339) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-01 18:52:22,340) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-01 18:52:22,340) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-01 18:52:22,341) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-01 18:52:22,341) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-01 18:52:22,343) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-01 18:52:22,344) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-01 18:52:22,345) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-01 18:52:22,345) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-01 18:52:22,346) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-01 18:52:22,346) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-01 18:52:22,347) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-01 18:52:22,348) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-01 18:52:22,348) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-01 18:52:22,349) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-01 18:52:22,349) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-01 18:52:22,350) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-01 18:52:22,350) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-01 18:52:22,351) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-01 18:52:22,352) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-01 18:52:22,352) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-01 18:52:22,353) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-01 18:52:22,353) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-01 18:52:22,354) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-01 18:52:22,354) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-01 18:52:22,356) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-01 18:52:22,357) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-01 18:52:22,357) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-01 18:52:22,358) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-01 18:52:22,359) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-01 18:52:22,359) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-01 18:52:22,360) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-01 18:52:22,360) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-01 18:52:22,361) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-01 18:52:22,362) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-01 18:52:22,362) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-01 18:52:22,363) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-01 18:52:22,364) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-01 18:52:22,364) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-01 18:52:22,365) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-01 18:52:22,365) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-01 18:52:22,366) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-01 18:52:22,367) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-01 18:52:22,367) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-01 18:52:22,368) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-01 18:52:22,369) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-01 18:52:22,369) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-01 18:52:22,370) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-01 18:52:22,371) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-01 18:52:22,371) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-01 18:52:22,372) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-01 18:52:22,372) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-01 18:52:22,373) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-01 18:52:22,374) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-01 18:52:22,374) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-01 18:52:22,375) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-01 18:52:22,375) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-01 18:52:22,378) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-01 18:52:22,379) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-01 18:52:22,380) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-01 18:52:22,380) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-01 18:52:22,381) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-01 18:52:22,382) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-01 18:52:22,382) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-01 18:52:22,383) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-01 18:52:22,384) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-01 18:52:22,384) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-01 18:52:22,385) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-01 18:52:22,385) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-01 18:52:22,385) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-01 18:52:22,386) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-01 18:52:22,387) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-01 18:52:22,387) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-01 18:52:22,388) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-01 18:52:22,388) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-01 18:52:22,389) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-01 18:52:22,389) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-01 18:52:22,390) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-01 18:52:22,391) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-01 18:52:22,392) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-01 18:52:22,392) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-01 18:52:22,393) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-01 18:52:22,393) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-01 18:52:22,396) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-01 18:52:22,396) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-01 18:52:22,397) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-01 18:52:22,398) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-01 18:52:22,399) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-01 18:52:22,400) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-01 18:52:22,757) [INFO]: Register evaluations
(2025-08-01 18:52:22,757) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:52:22,758) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:52:22,828) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 18:52:22,828) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 18:52:29,307) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-01 18:52:29,307) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-01 18:52:29,413) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:29,962) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,107) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,223) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,333) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,368) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,478) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,660) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,660) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,772) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,772) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:30,887) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,036) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,294) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,295) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,296) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,296) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,296) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,410) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,558) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,708) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,708) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,975) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:31,975) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,087) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,202) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,312) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,313) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,503) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,613) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,883) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,883) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,884) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,884) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,923) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:32,924) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:33,153) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:33,374) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:33,606) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:33,876) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:34,187) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:34,467) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:34,467) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:34,661) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:34,662) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:35,169) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:35,326) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:35,327) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:35,405) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:52:35,417) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:52:35,417) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:52:35,478) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 18:52:44,314) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-01 18:56:37,009) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-01 18:56:37,010) [INFO]: Dataset Directory has been loaded.
(2025-08-01 18:56:37,017) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:56:37,018) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-01 18:56:37,018) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:56:37,414) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-01 18:56:37,416) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-01 18:56:37,418) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-01 18:56:37,420) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-01 18:56:37,422) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-01 18:56:37,426) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-01 18:56:37,428) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-01 18:56:37,430) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-01 18:56:37,431) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-01 18:56:37,434) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-01 18:56:37,435) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-01 18:56:37,437) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-01 18:56:37,439) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-01 18:56:37,440) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-01 18:56:37,442) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-01 18:56:37,446) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-01 18:56:37,449) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-01 18:56:37,450) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-01 18:56:37,455) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-01 18:56:37,456) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-01 18:56:37,457) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-01 18:56:37,458) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-01 18:56:37,459) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-01 18:56:37,460) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-01 18:56:37,461) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-01 18:56:37,463) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-01 18:56:37,466) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-01 18:56:37,467) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-01 18:56:37,469) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-01 18:56:37,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-01 18:56:37,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-01 18:56:37,474) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-01 18:56:37,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-01 18:56:37,476) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-01 18:56:37,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-01 18:56:37,479) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-01 18:56:37,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-01 18:56:37,481) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-01 18:56:37,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-01 18:56:37,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-01 18:56:37,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-01 18:56:37,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-01 18:56:37,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-01 18:56:37,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-01 18:56:37,487) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-01 18:56:37,488) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-01 18:56:37,488) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-01 18:56:37,489) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-01 18:56:37,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-01 18:56:37,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-01 18:56:37,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-01 18:56:37,492) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-01 18:56:37,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-01 18:56:37,494) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-01 18:56:37,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-01 18:56:37,496) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-01 18:56:37,497) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-01 18:56:37,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-01 18:56:37,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-01 18:56:37,501) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-01 18:56:37,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-01 18:56:37,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-01 18:56:37,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-01 18:56:37,506) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-01 18:56:37,507) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-01 18:56:37,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-01 18:56:37,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-01 18:56:37,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-01 18:56:37,510) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-01 18:56:37,511) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-01 18:56:37,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-01 18:56:37,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-01 18:56:37,513) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-01 18:56:37,514) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-01 18:56:37,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-01 18:56:37,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-01 18:56:37,516) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-01 18:56:37,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-01 18:56:37,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-01 18:56:37,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-01 18:56:37,519) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-01 18:56:37,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-01 18:56:37,524) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-01 18:56:37,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-01 18:56:37,527) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-01 18:56:37,528) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-01 18:56:37,529) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-01 18:56:37,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-01 18:56:37,532) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-01 18:56:37,533) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-01 18:56:37,534) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-01 18:56:37,534) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-01 18:56:37,535) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-01 18:56:37,535) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-01 18:56:37,536) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-01 18:56:37,536) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-01 18:56:37,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-01 18:56:37,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-01 18:56:37,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-01 18:56:37,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-01 18:56:37,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-01 18:56:37,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-01 18:56:37,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-01 18:56:37,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-01 18:56:37,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-01 18:56:37,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-01 18:56:37,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-01 18:56:37,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-01 18:56:37,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-01 18:56:37,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-01 18:56:37,548) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-01 18:56:37,549) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-01 18:56:37,550) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-01 18:56:37,551) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-01 18:56:37,551) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-01 18:56:37,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-01 18:56:37,553) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-01 18:56:37,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-01 18:56:37,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-01 18:56:37,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-01 18:56:37,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-01 18:56:37,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-01 18:56:37,557) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-01 18:56:37,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-01 18:56:37,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-01 18:56:37,559) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-01 18:56:37,559) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-01 18:56:37,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-01 18:56:37,561) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-01 18:56:37,561) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-01 18:56:37,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-01 18:56:37,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-01 18:56:37,563) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-01 18:56:37,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-01 18:56:37,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-01 18:56:37,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-01 18:56:37,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-01 18:56:37,567) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-01 18:56:37,568) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-01 18:56:37,569) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-01 18:56:37,569) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-01 18:56:37,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-01 18:56:37,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-01 18:56:37,571) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-01 18:56:37,572) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-01 18:56:37,573) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-01 18:56:37,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-01 18:56:37,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-01 18:56:37,575) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-01 18:56:37,576) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-01 18:56:37,576) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-01 18:56:37,577) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-01 18:56:37,578) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-01 18:56:37,578) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-01 18:56:37,580) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-01 18:56:37,581) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-01 18:56:37,582) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-01 18:56:37,582) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-01 18:56:37,583) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-01 18:56:37,583) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-01 18:56:37,585) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-01 18:56:37,585) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-01 18:56:37,586) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-01 18:56:37,587) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-01 18:56:37,587) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-01 18:56:37,589) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-01 18:56:37,591) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-01 18:56:37,592) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-01 18:56:37,593) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-01 18:56:37,594) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-01 18:56:37,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-01 18:56:37,596) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-01 18:56:37,597) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-01 18:56:37,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-01 18:56:37,599) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-01 18:56:37,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-01 18:56:37,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-01 18:56:37,601) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-01 18:56:37,602) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-01 18:56:37,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-01 18:56:37,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-01 18:56:37,604) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-01 18:56:37,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-01 18:56:37,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-01 18:56:37,606) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-01 18:56:37,608) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-01 18:56:37,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-01 18:56:37,610) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-01 18:56:37,612) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-01 18:56:37,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-01 18:56:37,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-01 18:56:37,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-01 18:56:37,615) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-01 18:56:37,616) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-01 18:56:37,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-01 18:56:37,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-01 18:56:37,618) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-01 18:56:37,619) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-01 18:56:37,620) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-01 18:56:37,621) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-01 18:56:37,621) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-01 18:56:37,622) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-01 18:56:37,623) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-01 18:56:37,624) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-01 18:56:37,625) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-01 18:56:37,626) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-01 18:56:37,627) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-01 18:56:37,627) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-01 18:56:37,628) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-01 18:56:37,629) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-01 18:56:37,630) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-01 18:56:37,630) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-01 18:56:37,631) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-01 18:56:37,633) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-01 18:56:37,634) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-01 18:56:37,634) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-01 18:56:37,635) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-01 18:56:37,636) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-01 18:56:37,638) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-01 18:56:37,638) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-01 18:56:37,639) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-01 18:56:37,639) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-01 18:56:37,640) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-01 18:56:37,640) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-01 18:56:37,641) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-01 18:56:37,642) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-01 18:56:37,642) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-01 18:56:37,643) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-01 18:56:37,644) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-01 18:56:37,645) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-01 18:56:37,645) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-01 18:56:37,646) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-01 18:56:37,647) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-01 18:56:37,647) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-01 18:56:37,648) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-01 18:56:37,649) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-01 18:56:37,649) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-01 18:56:37,650) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-01 18:56:37,651) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-01 18:56:38,016) [INFO]: Register evaluations
(2025-08-01 18:56:38,016) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:56:38,017) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:56:38,084) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-01 18:56:38,085) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-01 18:56:44,441) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-01 18:56:44,441) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-01 18:56:44,554) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,120) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,270) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,380) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,489) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,524) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,636) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,817) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,818) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,927) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:45,928) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,040) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,188) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,445) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,446) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,446) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,447) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,447) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,560) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,710) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,859) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:46,860) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,132) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,132) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,244) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,364) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,470) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,471) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,662) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:47,772) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,034) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,034) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,035) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,035) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,074) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,074) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,298) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,514) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:48,749) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:49,010) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:49,314) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:49,583) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:49,584) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:49,766) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:49,767) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:50,256) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:50,406) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:50,407) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:50,482) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-01 18:56:50,493) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-01 18:56:50,494) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-01 18:56:50,555) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-01 18:56:59,586) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-11 17:22:48,619) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-11 17:22:48,622) [INFO]: Dataset Directory has been loaded.
(2025-08-11 17:22:54,057) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-11 17:22:54,059) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-11 17:22:54,060) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-11 17:26:42,164) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-11 17:26:42,165) [INFO]: Dataset Directory has been loaded.
(2025-08-11 17:26:46,005) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-11 17:26:46,006) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-11 17:26:46,007) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-11 17:26:46,410) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-11 17:26:46,412) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-11 17:26:46,414) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-11 17:26:46,415) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-11 17:26:46,417) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-11 17:26:46,419) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-11 17:26:46,421) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-11 17:26:46,424) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-11 17:26:46,426) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-11 17:26:46,427) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-11 17:26:46,429) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-11 17:26:46,431) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-11 17:26:46,432) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-11 17:26:46,433) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-11 17:26:46,434) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-11 17:26:46,438) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-11 17:26:46,439) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-11 17:26:46,441) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-11 17:26:46,442) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-11 17:26:46,442) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-11 17:26:46,444) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-11 17:26:46,445) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-11 17:26:46,448) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-11 17:26:46,448) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-11 17:26:46,449) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-11 17:26:46,451) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-11 17:26:46,453) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-11 17:26:46,456) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-11 17:26:46,457) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-11 17:26:46,459) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-11 17:26:46,459) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-11 17:26:46,460) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-11 17:26:46,461) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-11 17:26:46,461) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-11 17:26:46,462) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-11 17:26:46,463) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-11 17:26:46,464) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-11 17:26:46,464) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-11 17:26:46,466) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-11 17:26:46,468) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-11 17:26:46,469) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-11 17:26:46,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-11 17:26:46,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-11 17:26:46,472) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-11 17:26:46,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-11 17:26:46,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-11 17:26:46,474) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-11 17:26:46,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-11 17:26:46,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-11 17:26:46,476) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-11 17:26:46,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-11 17:26:46,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-11 17:26:46,478) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-11 17:26:46,479) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-11 17:26:46,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-11 17:26:46,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-11 17:26:46,481) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-11 17:26:46,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-11 17:26:46,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-11 17:26:46,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-11 17:26:46,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-11 17:26:46,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-11 17:26:46,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-11 17:26:46,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-11 17:26:46,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-11 17:26:46,487) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-11 17:26:46,489) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-11 17:26:46,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-11 17:26:46,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-11 17:26:46,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-11 17:26:46,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-11 17:26:46,492) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-11 17:26:46,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-11 17:26:46,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-11 17:26:46,494) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-11 17:26:46,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-11 17:26:46,496) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-11 17:26:46,497) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-11 17:26:46,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-11 17:26:46,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-11 17:26:46,499) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-11 17:26:46,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-11 17:26:46,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-11 17:26:46,501) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-11 17:26:46,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-11 17:26:46,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-11 17:26:46,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-11 17:26:46,504) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-11 17:26:46,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-11 17:26:46,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-11 17:26:46,506) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-11 17:26:46,507) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-11 17:26:46,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-11 17:26:46,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-11 17:26:46,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-11 17:26:46,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-11 17:26:46,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-11 17:26:46,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-11 17:26:46,513) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-11 17:26:46,514) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-11 17:26:46,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-11 17:26:46,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-11 17:26:46,516) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-11 17:26:46,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-11 17:26:46,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-11 17:26:46,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-11 17:26:46,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-11 17:26:46,519) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-11 17:26:46,520) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-11 17:26:46,520) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-11 17:26:46,521) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-11 17:26:46,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-11 17:26:46,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-11 17:26:46,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-11 17:26:46,524) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-11 17:26:46,526) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-11 17:26:46,528) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-11 17:26:46,529) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-11 17:26:46,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-11 17:26:46,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-11 17:26:46,532) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-11 17:26:46,533) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-11 17:26:46,535) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-11 17:26:46,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-11 17:26:46,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-11 17:26:46,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-11 17:26:46,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-11 17:26:46,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-11 17:26:46,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-11 17:26:46,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-11 17:26:46,544) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-11 17:26:46,545) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-11 17:26:46,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-11 17:26:46,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-11 17:26:46,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-11 17:26:46,548) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-11 17:26:46,549) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-11 17:26:46,550) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-11 17:26:46,551) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-11 17:26:46,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-11 17:26:46,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-11 17:26:46,553) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-11 17:26:46,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-11 17:26:46,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-11 17:26:46,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-11 17:26:46,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-11 17:26:46,557) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-11 17:26:46,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-11 17:26:46,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-11 17:26:46,559) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-11 17:26:46,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-11 17:26:46,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-11 17:26:46,561) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-11 17:26:46,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-11 17:26:46,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-11 17:26:46,563) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-11 17:26:46,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-11 17:26:46,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-11 17:26:46,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-11 17:26:46,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-11 17:26:46,566) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-11 17:26:46,567) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-11 17:26:46,567) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-11 17:26:46,568) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-11 17:26:46,569) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-11 17:26:46,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-11 17:26:46,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-11 17:26:46,571) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-11 17:26:46,572) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-11 17:26:46,573) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-11 17:26:46,573) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-11 17:26:46,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-11 17:26:46,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-11 17:26:46,575) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-11 17:26:46,575) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-11 17:26:46,576) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-11 17:26:46,577) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-11 17:26:46,577) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-11 17:26:46,578) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-11 17:26:46,579) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-11 17:26:46,581) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-11 17:26:46,581) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-11 17:26:46,582) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-11 17:26:46,582) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-11 17:26:46,583) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-11 17:26:46,584) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-11 17:26:46,584) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-11 17:26:46,585) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-11 17:26:46,586) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-11 17:26:46,587) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-11 17:26:46,587) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-11 17:26:46,588) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-11 17:26:46,589) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-11 17:26:46,589) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-11 17:26:46,590) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-11 17:26:46,591) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-11 17:26:46,591) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-11 17:26:46,592) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-11 17:26:46,592) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-11 17:26:46,593) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-11 17:26:46,594) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-11 17:26:46,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-11 17:26:46,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-11 17:26:46,596) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-11 17:26:46,596) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-11 17:26:46,597) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-11 17:26:46,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-11 17:26:46,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-11 17:26:46,599) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-11 17:26:46,599) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-11 17:26:46,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-11 17:26:46,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-11 17:26:46,601) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-11 17:26:46,602) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-11 17:26:46,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-11 17:26:46,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-11 17:26:46,604) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-11 17:26:46,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-11 17:26:46,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-11 17:26:46,606) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-11 17:26:46,606) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-11 17:26:46,607) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-11 17:26:46,607) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-11 17:26:46,608) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-11 17:26:46,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-11 17:26:46,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-11 17:26:46,610) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-11 17:26:46,611) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-11 17:26:46,611) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-11 17:26:46,612) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-11 17:26:46,612) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-11 17:26:46,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-11 17:26:46,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-11 17:26:46,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-11 17:26:46,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-11 17:26:46,615) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-11 17:26:46,616) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-11 17:26:46,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-11 17:26:46,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-11 17:26:47,813) [INFO]: Register evaluations
(2025-08-11 17:26:47,814) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-11 17:26:47,814) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-11 17:26:47,880) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-11 17:26:47,880) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-11 17:26:54,045) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-11 17:26:54,046) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-11 17:26:54,145) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:54,678) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:54,815) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:54,920) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,025) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,059) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,168) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,352) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,352) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,457) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,458) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,569) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,711) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,962) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,962) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,963) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,963) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:55,964) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,074) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,215) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,360) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,361) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,622) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,622) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,731) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,842) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,943) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:56,944) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,129) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,233) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,498) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,499) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,499) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,500) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,538) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,539) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,761) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:57,976) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:58,207) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:58,467) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:58,773) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,044) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,045) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,227) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,228) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,714) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,865) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,866) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,943) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-11 17:26:59,953) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-11 17:26:59,953) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-11 17:27:00,012) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-11 17:27:09,230) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-12 19:36:57,924) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-12 19:36:57,929) [INFO]: Dataset Directory has been loaded.
(2025-08-12 19:39:24,789) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-12 19:39:24,790) [INFO]: Dataset Directory has been loaded.
(2025-08-12 19:39:24,806) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-12 19:39:24,807) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-12 19:39:24,808) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 19:39:25,183) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-12 19:39:50,200) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-12 19:40:15,515) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-12 19:40:37,973) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-12 19:41:03,508) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-12 19:41:29,118) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-12 19:41:47,857) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-12 19:42:06,742) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-12 19:42:29,217) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-12 19:42:54,855) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-12 19:42:55,604) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-12 19:43:18,110) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-12 19:43:43,650) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-12 19:43:44,399) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-12 19:44:03,089) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-12 19:44:21,384) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-12 19:44:47,202) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-12 19:45:13,229) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-12 19:45:13,986) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-12 19:45:15,377) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-12 19:45:38,122) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-12 19:45:38,857) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-12 19:46:01,401) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-12 19:46:02,131) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-12 19:46:02,874) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-12 19:46:28,572) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-12 19:46:54,254) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-12 19:47:16,717) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-12 19:47:42,242) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-12 19:48:04,828) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-12 19:48:06,737) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-12 19:48:09,137) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-12 19:48:11,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-12 19:48:13,912) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-12 19:48:16,228) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-12 19:48:18,635) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-12 19:48:21,053) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-12 19:48:23,455) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-12 19:48:25,834) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-12 19:48:28,230) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-12 19:48:30,640) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-12 19:48:33,030) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-12 19:48:35,374) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-12 19:48:37,762) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-12 19:48:40,134) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-12 19:48:42,534) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-12 19:48:44,920) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-12 19:48:47,306) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-12 19:48:49,690) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-12 19:48:52,073) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-12 19:48:54,450) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-12 19:48:56,837) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-12 19:48:59,193) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-12 19:49:01,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-12 19:49:03,945) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-12 19:49:06,349) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-12 19:49:08,692) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-12 19:49:11,186) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-12 19:49:13,689) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-12 19:49:16,059) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-12 19:49:18,439) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-12 19:49:20,746) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-12 19:49:23,115) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-12 19:49:25,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-12 19:49:27,864) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-12 19:49:30,249) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-12 19:49:32,629) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-12 19:49:35,002) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-12 19:49:37,409) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-12 19:49:39,787) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-12 19:49:42,175) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-12 19:49:44,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-12 19:49:46,940) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-12 19:49:49,300) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-12 19:49:51,675) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-12 19:49:54,037) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-12 19:49:56,411) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-12 19:49:58,784) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-12 19:50:01,170) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-12 19:50:03,504) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-12 19:50:05,882) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-12 19:50:08,247) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-12 19:50:10,618) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-12 19:50:12,996) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-12 19:50:15,378) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-12 19:50:17,770) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-12 19:50:20,142) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-12 19:50:22,628) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-12 19:50:25,030) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-12 19:50:27,465) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-12 19:50:29,848) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-12 19:50:32,237) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-12 19:50:34,662) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-12 19:50:37,070) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-12 19:50:39,456) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-12 19:50:41,847) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-12 19:50:44,237) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-12 19:50:46,625) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-12 19:50:48,977) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-12 19:50:51,379) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-12 19:50:53,785) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-12 19:50:56,176) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-12 19:50:58,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-12 19:51:00,958) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-12 19:51:03,360) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-12 19:51:05,750) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-12 19:51:08,178) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-12 19:51:10,584) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-12 19:51:12,992) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-12 19:51:15,424) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-12 19:51:17,812) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-12 19:51:20,196) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-12 19:51:22,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-12 19:51:24,945) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-12 19:51:27,333) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-12 19:51:29,708) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-12 19:51:32,132) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-12 19:51:34,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-12 19:51:36,974) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-12 19:51:39,361) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-12 19:51:41,734) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-12 19:51:44,123) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-12 19:51:46,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-12 19:51:48,856) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-12 19:51:51,229) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-12 19:51:53,602) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-12 19:51:55,978) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-12 19:51:58,348) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-12 19:52:00,731) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-12 19:52:03,116) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-12 19:52:05,501) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-12 19:52:07,923) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-12 19:52:10,311) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-12 19:52:12,691) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-12 19:52:15,086) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-12 19:52:17,641) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-12 19:52:20,074) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-12 19:52:22,481) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-12 19:52:24,862) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-12 19:52:27,233) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-12 19:52:29,623) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-12 19:52:31,998) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-12 19:52:34,368) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-12 19:52:36,762) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-12 19:52:39,167) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-12 19:52:41,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-12 19:52:43,903) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-12 19:52:46,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-12 19:52:48,650) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-12 19:52:51,627) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-12 19:52:54,023) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-12 19:52:56,393) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-12 19:52:58,770) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-12 19:53:01,152) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-12 19:53:03,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-12 19:53:05,930) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-12 19:53:08,307) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-12 19:53:10,694) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-12 19:53:12,997) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-12 19:53:15,385) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-12 19:53:17,772) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-12 19:53:20,147) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-12 19:53:22,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-12 19:53:24,892) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-12 19:53:27,270) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-12 19:53:29,642) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-12 19:53:32,012) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-12 19:53:34,388) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-12 19:53:36,786) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-12 19:53:39,183) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-12 19:53:41,589) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-12 19:53:44,106) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-12 19:53:46,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-12 19:53:48,892) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-12 19:53:51,289) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-12 19:53:53,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-12 19:53:55,959) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-12 19:53:58,327) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-12 19:54:00,724) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-12 19:54:03,105) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-12 19:54:05,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-12 19:54:07,885) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-12 19:54:10,272) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-12 19:54:12,665) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-12 19:54:15,044) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-12 19:54:17,428) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-12 19:54:19,828) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-12 19:54:22,215) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-12 19:54:24,727) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-12 19:54:27,147) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-12 19:54:29,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-12 19:54:31,926) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-12 19:54:34,326) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-12 19:54:36,734) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-12 19:54:39,164) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-12 19:54:41,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-12 19:54:43,996) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-12 19:54:46,427) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-12 19:54:48,839) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-12 19:54:51,226) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-12 19:54:53,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-12 19:54:56,000) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-12 19:54:58,381) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-12 19:55:00,811) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-12 19:55:03,207) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-12 19:55:06,069) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-12 19:55:08,451) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-12 19:55:10,854) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-12 19:55:13,268) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-12 19:55:15,661) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-12 19:55:18,069) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-12 19:55:20,373) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-12 19:55:22,723) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-12 19:55:25,094) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-12 19:55:27,466) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-12 19:55:29,872) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-12 19:55:32,260) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-12 19:55:34,654) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-12 19:55:37,045) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-12 19:55:39,432) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-12 19:55:41,829) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-12 19:55:44,212) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-12 19:55:46,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-12 19:55:48,896) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-12 19:55:51,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-12 19:55:53,639) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-12 19:55:55,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-12 19:55:57,906) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-12 19:56:00,280) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-12 19:56:02,653) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-12 19:56:05,023) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-12 19:56:07,400) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-12 19:56:09,803) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-12 19:56:12,217) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-12 19:56:14,618) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-12 19:56:17,002) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-12 19:56:19,395) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-12 19:56:21,774) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-12 19:56:24,161) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-12 19:56:26,975) [INFO]: Register evaluations
(2025-08-12 19:56:26,976) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-12 19:56:26,976) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 19:56:27,378) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-12 19:56:27,379) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-12 20:47:50,262) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:47:50,264) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-12 20:47:50,265) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:47:50,679) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-12 20:48:17,550) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-12 20:48:44,685) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-12 20:51:36,117) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:51:36,118) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-12 20:51:36,119) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:51:36,550) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-12 20:51:36,552) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-12 20:51:36,553) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-12 20:51:36,555) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-12 20:51:36,557) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-12 20:51:36,558) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-12 20:51:36,560) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-12 20:51:36,562) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-12 20:51:36,563) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-12 20:51:36,565) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-12 20:51:36,566) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-12 20:51:36,567) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-12 20:51:36,569) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-12 20:51:36,569) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-12 20:51:36,574) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-12 20:51:36,576) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-12 20:51:36,578) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-12 20:51:36,580) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-12 20:51:36,581) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-12 20:51:36,582) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-12 20:51:36,583) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-12 20:51:36,583) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-12 20:51:36,585) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-12 20:51:36,585) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-12 20:51:36,586) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-12 20:51:36,588) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-12 20:51:36,589) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-12 20:51:36,591) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-12 20:51:36,593) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-12 20:51:36,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-12 20:51:36,596) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-12 20:51:36,597) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-12 20:51:36,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-12 20:51:36,599) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-12 20:51:36,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-12 20:51:36,601) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-12 20:51:36,602) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-12 20:51:36,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-12 20:51:36,604) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-12 20:51:36,604) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-12 20:51:36,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-12 20:51:36,606) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-12 20:51:36,607) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-12 20:51:36,608) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-12 20:51:36,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-12 20:51:36,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-12 20:51:36,610) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-12 20:51:36,611) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-12 20:51:36,612) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-12 20:51:36,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-12 20:51:36,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-12 20:51:36,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-12 20:51:36,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-12 20:51:36,615) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-12 20:51:36,616) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-12 20:51:36,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-12 20:51:36,618) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-12 20:51:36,619) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-12 20:51:36,620) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-12 20:51:36,620) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-12 20:51:36,621) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-12 20:51:36,622) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-12 20:51:36,622) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-12 20:51:36,623) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-12 20:51:36,624) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-12 20:51:36,625) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-12 20:51:36,625) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-12 20:51:36,626) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-12 20:51:36,627) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-12 20:51:36,628) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-12 20:51:36,635) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-12 20:51:36,637) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-12 20:51:36,638) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-12 20:51:36,639) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-12 20:51:36,639) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-12 20:51:36,640) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-12 20:51:36,641) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-12 20:51:36,642) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-12 20:51:36,642) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-12 20:51:36,643) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-12 20:51:36,644) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-12 20:51:36,644) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-12 20:51:36,645) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-12 20:51:36,646) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-12 20:51:36,647) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-12 20:51:36,648) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-12 20:51:36,649) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-12 20:51:36,650) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-12 20:51:36,651) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-12 20:51:36,652) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-12 20:51:36,653) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-12 20:51:36,654) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-12 20:51:36,654) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-12 20:51:36,655) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-12 20:51:36,656) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-12 20:51:36,657) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-12 20:51:36,658) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-12 20:51:36,659) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-12 20:51:36,660) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-12 20:51:36,660) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-12 20:51:36,662) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-12 20:51:36,663) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-12 20:51:36,664) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-12 20:51:36,665) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-12 20:51:36,666) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-12 20:51:36,667) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-12 20:51:36,667) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-12 20:51:36,668) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-12 20:51:36,669) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-12 20:51:36,670) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-12 20:51:36,671) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-12 20:51:36,672) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-12 20:51:36,673) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-12 20:51:36,675) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-12 20:51:36,675) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-12 20:51:36,676) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-12 20:51:36,677) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-12 20:51:36,678) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-12 20:51:36,679) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-12 20:51:36,680) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-12 20:51:36,681) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-12 20:51:36,682) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-12 20:51:36,683) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-12 20:51:36,684) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-12 20:51:36,685) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-12 20:51:36,685) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-12 20:51:36,686) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-12 20:51:36,687) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-12 20:51:36,688) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-12 20:51:36,689) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-12 20:51:36,690) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-12 20:51:36,690) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-12 20:51:36,691) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-12 20:51:36,692) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-12 20:51:36,693) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-12 20:51:36,693) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-12 20:51:36,694) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-12 20:51:36,695) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-12 20:51:36,696) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-12 20:51:36,697) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-12 20:51:36,697) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-12 20:51:36,698) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-12 20:51:36,699) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-12 20:51:36,700) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-12 20:51:36,702) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-12 20:51:36,703) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-12 20:51:36,704) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-12 20:51:36,705) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-12 20:51:36,706) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-12 20:51:36,706) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-12 20:51:36,707) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-12 20:51:36,708) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-12 20:51:36,708) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-12 20:51:36,709) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-12 20:51:36,709) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-12 20:51:36,710) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-12 20:51:36,711) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-12 20:51:36,712) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-12 20:51:36,714) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-12 20:51:36,717) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-12 20:51:36,717) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-12 20:51:36,718) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-12 20:51:36,719) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-12 20:51:36,720) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-12 20:51:36,721) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-12 20:51:36,721) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-12 20:51:36,722) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-12 20:51:36,723) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-12 20:51:36,724) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-12 20:51:36,724) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-12 20:51:36,725) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-12 20:51:36,726) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-12 20:51:36,727) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-12 20:51:36,728) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-12 20:51:36,728) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-12 20:51:36,729) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-12 20:51:36,730) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-12 20:51:36,731) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-12 20:51:36,731) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-12 20:51:36,732) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-12 20:51:36,734) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-12 20:51:36,737) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-12 20:51:36,737) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-12 20:51:36,738) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-12 20:51:36,739) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-12 20:51:36,740) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-12 20:51:36,740) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-12 20:51:36,741) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-12 20:51:36,742) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-12 20:51:36,742) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-12 20:51:36,743) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-12 20:51:36,744) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-12 20:51:36,745) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-12 20:51:36,746) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-12 20:51:36,747) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-12 20:51:36,748) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-12 20:51:36,748) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-12 20:51:36,749) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-12 20:51:36,750) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-12 20:51:36,751) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-12 20:51:36,752) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-12 20:51:36,753) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-12 20:51:36,753) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-12 20:51:36,754) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-12 20:51:36,755) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-12 20:51:36,755) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-12 20:51:36,756) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-12 20:51:36,757) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-12 20:51:36,757) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-12 20:51:36,758) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-12 20:51:36,759) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-12 20:51:36,760) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-12 20:51:36,760) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-12 20:51:36,761) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-12 20:51:36,762) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-12 20:51:36,763) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-12 20:51:36,763) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-12 20:51:36,764) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-12 20:51:36,765) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-12 20:51:36,766) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-12 20:51:36,766) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-12 20:51:36,767) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-12 20:51:36,768) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-12 20:51:36,769) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-12 20:51:36,769) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-12 20:51:36,770) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-12 20:51:36,771) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-12 20:51:36,771) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-12 20:51:36,772) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-12 20:51:36,773) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-12 20:51:36,774) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-12 20:51:36,774) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-12 20:51:36,775) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-12 20:51:36,776) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-12 20:51:36,777) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-12 20:51:36,777) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-12 20:51:36,778) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-12 20:51:36,779) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-12 20:51:36,780) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-12 20:51:36,781) [INFO]: Register evaluations
(2025-08-12 20:51:36,781) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:51:36,781) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:51:36,870) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-12 20:51:36,870) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-12 20:51:51,588) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:51:51,589) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-12 20:51:51,590) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:51:51,727) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-12 20:51:51,729) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-12 20:51:51,730) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-12 20:51:51,731) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-12 20:51:51,732) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-12 20:51:51,734) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-12 20:51:51,735) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-12 20:51:51,736) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-12 20:51:51,738) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-12 20:51:51,739) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-12 20:51:51,739) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-12 20:51:51,741) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-12 20:51:51,742) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-12 20:51:51,743) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-12 20:51:51,745) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-12 20:51:51,746) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-12 20:51:51,747) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-12 20:51:51,748) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-12 20:51:51,749) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-12 20:51:51,750) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-12 20:51:51,751) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-12 20:51:51,752) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-12 20:51:51,753) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-12 20:51:51,754) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-12 20:51:51,754) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-12 20:51:51,756) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-12 20:51:51,757) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-12 20:51:51,758) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-12 20:51:51,759) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-12 20:51:51,760) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-12 20:51:51,762) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-12 20:51:51,763) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-12 20:51:51,764) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-12 20:51:51,765) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-12 20:51:51,765) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-12 20:51:51,766) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-12 20:51:51,767) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-12 20:51:51,768) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-12 20:51:51,769) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-12 20:51:51,770) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-12 20:51:51,770) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-12 20:51:51,771) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-12 20:51:51,772) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-12 20:51:51,772) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-12 20:51:51,773) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-12 20:51:51,774) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-12 20:51:51,775) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-12 20:51:51,775) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-12 20:51:51,776) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-12 20:51:51,777) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-12 20:51:51,778) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-12 20:51:51,779) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-12 20:51:51,779) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-12 20:51:51,780) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-12 20:51:51,781) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-12 20:51:51,781) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-12 20:51:51,782) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-12 20:51:51,783) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-12 20:51:51,784) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-12 20:51:51,785) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-12 20:51:51,785) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-12 20:51:51,786) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-12 20:51:51,786) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-12 20:51:51,787) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-12 20:51:51,788) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-12 20:51:51,788) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-12 20:51:51,789) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-12 20:51:51,790) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-12 20:51:51,790) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-12 20:51:51,791) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-12 20:51:51,792) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-12 20:51:51,792) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-12 20:51:51,793) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-12 20:51:51,794) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-12 20:51:51,794) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-12 20:51:51,795) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-12 20:51:51,796) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-12 20:51:51,797) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-12 20:51:51,798) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-12 20:51:51,799) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-12 20:51:51,799) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-12 20:51:51,800) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-12 20:51:51,801) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-12 20:51:51,802) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-12 20:51:51,802) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-12 20:51:51,803) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-12 20:51:51,803) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-12 20:51:51,804) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-12 20:51:51,805) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-12 20:51:51,806) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-12 20:51:51,806) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-12 20:51:51,807) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-12 20:51:51,808) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-12 20:51:51,808) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-12 20:51:51,809) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-12 20:51:51,810) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-12 20:51:51,811) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-12 20:51:51,812) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-12 20:51:51,813) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-12 20:51:51,814) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-12 20:51:51,814) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-12 20:51:51,815) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-12 20:51:51,816) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-12 20:51:51,817) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-12 20:51:51,818) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-12 20:51:51,819) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-12 20:51:51,819) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-12 20:51:51,820) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-12 20:51:51,821) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-12 20:51:51,822) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-12 20:51:51,822) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-12 20:51:51,823) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-12 20:51:51,824) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-12 20:51:51,825) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-12 20:51:51,825) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-12 20:51:51,826) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-12 20:51:51,826) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-12 20:51:51,827) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-12 20:51:51,828) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-12 20:51:51,829) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-12 20:51:51,829) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-12 20:51:51,830) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-12 20:51:51,831) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-12 20:51:51,832) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-12 20:51:51,832) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-12 20:51:51,833) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-12 20:51:51,834) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-12 20:51:51,834) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-12 20:51:51,835) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-12 20:51:51,836) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-12 20:51:51,837) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-12 20:51:51,837) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-12 20:51:51,838) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-12 20:51:51,838) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-12 20:51:51,839) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-12 20:51:51,840) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-12 20:51:51,841) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-12 20:51:51,841) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-12 20:51:51,842) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-12 20:51:51,843) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-12 20:51:51,843) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-12 20:51:51,844) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-12 20:51:51,845) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-12 20:51:51,846) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-12 20:51:51,846) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-12 20:51:51,848) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-12 20:51:51,849) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-12 20:51:51,850) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-12 20:51:51,851) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-12 20:51:51,852) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-12 20:51:51,853) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-12 20:51:51,853) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-12 20:51:51,854) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-12 20:51:51,855) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-12 20:51:51,856) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-12 20:51:51,856) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-12 20:51:51,857) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-12 20:51:51,858) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-12 20:51:51,859) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-12 20:51:51,859) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-12 20:51:51,860) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-12 20:51:51,861) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-12 20:51:51,862) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-12 20:51:51,863) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-12 20:51:51,863) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-12 20:51:51,864) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-12 20:51:51,865) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-12 20:51:51,866) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-12 20:51:51,866) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-12 20:51:51,867) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-12 20:51:51,867) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-12 20:51:51,868) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-12 20:51:51,869) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-12 20:51:51,869) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-12 20:51:51,870) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-12 20:51:51,870) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-12 20:51:51,871) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-12 20:51:51,872) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-12 20:51:51,872) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-12 20:51:51,873) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-12 20:51:51,874) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-12 20:51:51,874) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-12 20:51:51,875) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-12 20:51:51,876) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-12 20:51:51,876) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-12 20:51:51,877) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-12 20:51:51,878) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-12 20:51:51,879) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-12 20:51:51,879) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-12 20:51:51,880) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-12 20:51:51,880) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-12 20:51:51,881) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-12 20:51:51,882) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-12 20:51:51,882) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-12 20:51:51,883) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-12 20:51:51,883) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-12 20:51:51,884) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-12 20:51:51,885) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-12 20:51:51,885) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-12 20:51:51,886) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-12 20:51:51,887) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-12 20:51:51,887) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-12 20:51:51,888) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-12 20:51:51,888) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-12 20:51:51,889) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-12 20:51:51,890) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-12 20:51:51,890) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-12 20:51:51,891) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-12 20:51:51,892) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-12 20:51:51,892) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-12 20:51:51,893) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-12 20:51:51,893) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-12 20:51:51,894) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-12 20:51:51,895) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-12 20:51:51,896) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-12 20:51:51,897) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-12 20:51:51,897) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-12 20:51:51,898) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-12 20:51:51,899) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-12 20:51:51,899) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-12 20:51:51,900) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-12 20:51:51,900) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-12 20:51:51,901) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-12 20:51:51,902) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-12 20:51:51,902) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-12 20:51:51,903) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-12 20:51:51,903) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-12 20:51:51,904) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-12 20:51:51,905) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-12 20:51:51,905) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-12 20:51:51,906) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-12 20:51:51,906) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-12 20:51:51,907) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-12 20:51:51,908) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-12 20:51:51,909) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-12 20:51:51,909) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-12 20:51:51,910) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-12 20:51:51,910) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-12 20:51:51,911) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-12 20:51:51,912) [INFO]: Register evaluations
(2025-08-12 20:51:51,913) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:51:51,913) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:51:52,016) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-12 20:51:52,016) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-12 20:51:56,228) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:51:56,229) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-12 20:51:56,230) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:51:56,322) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-12 20:51:56,323) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-12 20:51:56,324) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-12 20:51:56,325) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-12 20:51:56,327) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-12 20:51:56,329) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-12 20:51:56,330) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-12 20:51:56,331) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-12 20:51:56,333) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-12 20:51:56,334) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-12 20:51:56,335) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-12 20:51:56,336) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-12 20:51:56,338) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-12 20:51:56,339) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-12 20:51:56,341) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-12 20:51:56,342) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-12 20:51:56,343) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-12 20:51:56,344) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-12 20:51:56,345) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-12 20:51:56,346) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-12 20:51:56,347) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-12 20:51:56,347) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-12 20:51:56,349) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-12 20:51:56,351) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-12 20:51:56,352) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-12 20:51:56,354) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-12 20:51:56,357) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-12 20:51:56,358) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-12 20:51:56,360) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-12 20:51:56,363) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-12 20:51:56,364) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-12 20:51:56,364) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-12 20:51:56,365) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-12 20:51:56,366) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-12 20:51:56,367) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-12 20:51:56,368) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-12 20:51:56,369) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-12 20:51:56,369) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-12 20:51:56,370) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-12 20:51:56,370) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-12 20:51:56,371) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-12 20:51:56,372) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-12 20:51:56,372) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-12 20:51:56,373) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-12 20:51:56,373) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-12 20:51:56,374) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-12 20:51:56,375) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-12 20:51:56,375) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-12 20:51:56,376) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-12 20:51:56,377) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-12 20:51:56,378) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-12 20:51:56,378) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-12 20:51:56,379) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-12 20:51:56,380) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-12 20:51:56,380) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-12 20:51:56,381) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-12 20:51:56,382) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-12 20:51:56,383) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-12 20:51:56,383) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-12 20:51:56,384) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-12 20:51:56,385) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-12 20:51:56,385) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-12 20:51:56,386) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-12 20:51:56,387) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-12 20:51:56,387) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-12 20:51:56,388) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-12 20:51:56,389) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-12 20:51:56,389) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-12 20:51:56,390) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-12 20:51:56,391) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-12 20:51:56,391) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-12 20:51:56,392) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-12 20:51:56,392) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-12 20:51:56,393) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-12 20:51:56,394) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-12 20:51:56,394) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-12 20:51:56,395) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-12 20:51:56,396) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-12 20:51:56,397) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-12 20:51:56,399) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-12 20:51:56,399) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-12 20:51:56,400) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-12 20:51:56,401) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-12 20:51:56,401) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-12 20:51:56,402) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-12 20:51:56,402) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-12 20:51:56,403) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-12 20:51:56,404) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-12 20:51:56,404) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-12 20:51:56,405) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-12 20:51:56,406) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-12 20:51:56,406) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-12 20:51:56,407) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-12 20:51:56,407) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-12 20:51:56,408) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-12 20:51:56,409) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-12 20:51:56,410) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-12 20:51:56,411) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-12 20:51:56,412) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-12 20:51:56,412) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-12 20:51:56,413) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-12 20:51:56,414) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-12 20:51:56,415) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-12 20:51:56,415) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-12 20:51:56,416) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-12 20:51:56,417) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-12 20:51:56,417) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-12 20:51:56,468) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-12 20:51:56,468) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-12 20:51:56,469) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-12 20:51:56,470) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-12 20:51:56,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-12 20:51:56,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-12 20:51:56,472) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-12 20:51:56,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-12 20:51:56,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-12 20:51:56,474) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-12 20:51:56,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-12 20:51:56,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-12 20:51:56,476) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-12 20:51:56,476) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-12 20:51:56,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-12 20:51:56,478) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-12 20:51:56,478) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-12 20:51:56,479) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-12 20:51:56,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-12 20:51:56,481) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-12 20:51:56,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-12 20:51:56,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-12 20:51:56,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-12 20:51:56,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-12 20:51:56,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-12 20:51:56,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-12 20:51:56,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-12 20:51:56,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-12 20:51:56,487) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-12 20:51:56,488) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-12 20:51:56,488) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-12 20:51:56,489) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-12 20:51:56,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-12 20:51:56,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-12 20:51:56,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-12 20:51:56,492) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-12 20:51:56,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-12 20:51:56,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-12 20:51:56,494) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-12 20:51:56,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-12 20:51:56,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-12 20:51:56,496) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-12 20:51:56,497) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-12 20:51:56,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-12 20:51:56,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-12 20:51:56,499) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-12 20:51:56,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-12 20:51:56,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-12 20:51:56,501) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-12 20:51:56,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-12 20:51:56,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-12 20:51:56,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-12 20:51:56,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-12 20:51:56,504) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-12 20:51:56,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-12 20:51:56,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-12 20:51:56,506) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-12 20:51:56,507) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-12 20:51:56,507) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-12 20:51:56,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-12 20:51:56,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-12 20:51:56,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-12 20:51:56,510) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-12 20:51:56,510) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-12 20:51:56,511) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-12 20:51:56,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-12 20:51:56,513) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-12 20:51:56,513) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-12 20:51:56,514) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-12 20:51:56,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-12 20:51:56,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-12 20:51:56,516) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-12 20:51:56,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-12 20:51:56,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-12 20:51:56,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-12 20:51:56,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-12 20:51:56,519) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-12 20:51:56,519) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-12 20:51:56,520) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-12 20:51:56,521) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-12 20:51:56,521) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-12 20:51:56,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-12 20:51:56,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-12 20:51:56,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-12 20:51:56,524) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-12 20:51:56,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-12 20:51:56,525) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-12 20:51:56,526) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-12 20:51:56,527) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-12 20:51:56,527) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-12 20:51:56,528) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-12 20:51:56,529) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-12 20:51:56,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-12 20:51:56,531) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-12 20:51:56,532) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-12 20:51:56,532) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-12 20:51:56,533) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-12 20:51:56,534) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-12 20:51:56,534) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-12 20:51:56,535) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-12 20:51:56,535) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-12 20:51:56,536) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-12 20:51:56,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-12 20:51:56,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-12 20:51:56,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-12 20:51:56,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-12 20:51:56,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-12 20:51:56,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-12 20:51:56,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-12 20:51:56,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-12 20:51:56,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-12 20:51:56,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-12 20:51:56,544) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-12 20:51:56,545) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-12 20:51:56,545) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-12 20:51:56,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-12 20:51:56,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-12 20:51:56,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-12 20:51:56,548) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-12 20:51:56,549) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-12 20:51:56,550) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-12 20:51:56,550) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-12 20:51:56,551) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-12 20:51:56,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-12 20:51:56,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-12 20:51:56,553) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-12 20:51:56,553) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-12 20:51:56,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-12 20:51:56,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-12 20:51:56,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-12 20:51:56,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-12 20:51:56,557) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-12 20:51:56,557) [INFO]: Register evaluations
(2025-08-12 20:51:56,558) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:51:56,558) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:51:56,629) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-12 20:51:56,630) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-12 20:53:45,789) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-12 20:53:45,790) [INFO]: Dataset Directory has been loaded.
(2025-08-12 20:53:45,802) [INFO]: Run Experiments. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:53:45,803) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-12 20:53:45,803) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:53:46,199) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-12 20:53:46,200) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-12 20:53:46,201) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-12 20:53:46,202) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-12 20:53:46,203) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-12 20:53:46,204) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-12 20:53:46,205) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-12 20:53:46,206) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-12 20:53:46,207) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-12 20:53:46,208) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-12 20:53:46,208) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-12 20:53:46,210) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-12 20:53:46,213) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-12 20:53:46,214) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-12 20:53:46,215) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-12 20:53:46,215) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-12 20:53:46,217) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-12 20:53:46,220) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-12 20:53:46,220) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-12 20:53:46,221) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-12 20:53:46,222) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-12 20:53:46,222) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-12 20:53:46,225) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-12 20:53:46,225) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-12 20:53:46,226) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-12 20:53:46,227) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-12 20:53:46,227) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-12 20:53:46,230) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-12 20:53:46,231) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-12 20:53:46,232) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 
(2025-08-12 20:53:46,236) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 
(2025-08-12 20:53:46,236) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 
(2025-08-12 20:53:46,237) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 
(2025-08-12 20:53:46,237) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 
(2025-08-12 20:53:46,238) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 
(2025-08-12 20:53:46,238) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 
(2025-08-12 20:53:46,239) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 
(2025-08-12 20:53:46,239) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 
(2025-08-12 20:53:46,240) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 
(2025-08-12 20:53:46,240) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 
(2025-08-12 20:53:46,241) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 
(2025-08-12 20:53:46,241) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 
(2025-08-12 20:53:46,241) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 
(2025-08-12 20:53:46,242) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 
(2025-08-12 20:53:46,243) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 
(2025-08-12 20:53:46,243) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 
(2025-08-12 20:53:46,243) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 
(2025-08-12 20:53:46,244) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 
(2025-08-12 20:53:46,244) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 
(2025-08-12 20:53:46,245) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 
(2025-08-12 20:53:46,245) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 
(2025-08-12 20:53:46,246) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 
(2025-08-12 20:53:46,246) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 
(2025-08-12 20:53:46,247) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 
(2025-08-12 20:53:46,247) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 
(2025-08-12 20:53:46,248) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 
(2025-08-12 20:53:46,248) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 
(2025-08-12 20:53:46,249) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 
(2025-08-12 20:53:46,249) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 
(2025-08-12 20:53:46,249) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 
(2025-08-12 20:53:46,250) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 
(2025-08-12 20:53:46,250) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 
(2025-08-12 20:53:46,251) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 
(2025-08-12 20:53:46,251) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 
(2025-08-12 20:53:46,252) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 
(2025-08-12 20:53:46,252) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 
(2025-08-12 20:53:46,253) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 
(2025-08-12 20:53:46,255) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 
(2025-08-12 20:53:46,255) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 
(2025-08-12 20:53:46,256) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 
(2025-08-12 20:53:46,256) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 
(2025-08-12 20:53:46,256) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 
(2025-08-12 20:53:46,257) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 
(2025-08-12 20:53:46,257) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 
(2025-08-12 20:53:46,258) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 
(2025-08-12 20:53:46,258) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 
(2025-08-12 20:53:46,259) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 
(2025-08-12 20:53:46,259) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 
(2025-08-12 20:53:46,260) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 
(2025-08-12 20:53:46,260) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 
(2025-08-12 20:53:46,260) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 
(2025-08-12 20:53:46,261) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 
(2025-08-12 20:53:46,261) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 
(2025-08-12 20:53:46,262) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 
(2025-08-12 20:53:46,262) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 
(2025-08-12 20:53:46,263) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 
(2025-08-12 20:53:46,263) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 
(2025-08-12 20:53:46,264) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 
(2025-08-12 20:53:46,264) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 
(2025-08-12 20:53:46,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 
(2025-08-12 20:53:46,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 
(2025-08-12 20:53:46,265) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 
(2025-08-12 20:53:46,266) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 
(2025-08-12 20:53:46,266) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 
(2025-08-12 20:53:46,267) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 
(2025-08-12 20:53:46,267) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 
(2025-08-12 20:53:46,268) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 
(2025-08-12 20:53:46,268) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 
(2025-08-12 20:53:46,269) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 
(2025-08-12 20:53:46,269) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 
(2025-08-12 20:53:46,270) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 
(2025-08-12 20:53:46,270) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 
(2025-08-12 20:53:46,270) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 
(2025-08-12 20:53:46,271) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 
(2025-08-12 20:53:46,271) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 
(2025-08-12 20:53:46,272) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 
(2025-08-12 20:53:46,272) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 
(2025-08-12 20:53:46,273) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 
(2025-08-12 20:53:46,273) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 
(2025-08-12 20:53:46,274) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 
(2025-08-12 20:53:46,274) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 
(2025-08-12 20:53:46,274) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 
(2025-08-12 20:53:46,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 
(2025-08-12 20:53:46,275) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 
(2025-08-12 20:53:46,276) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 
(2025-08-12 20:53:46,276) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 
(2025-08-12 20:53:46,277) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 
(2025-08-12 20:53:46,277) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 
(2025-08-12 20:53:46,278) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 
(2025-08-12 20:53:46,278) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 
(2025-08-12 20:53:46,279) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 
(2025-08-12 20:53:46,279) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 
(2025-08-12 20:53:46,279) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 
(2025-08-12 20:53:46,280) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 
(2025-08-12 20:53:46,280) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 
(2025-08-12 20:53:46,281) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 
(2025-08-12 20:53:46,282) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 
(2025-08-12 20:53:46,282) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 
(2025-08-12 20:53:46,283) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 
(2025-08-12 20:53:46,283) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 
(2025-08-12 20:53:46,284) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 
(2025-08-12 20:53:46,284) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 
(2025-08-12 20:53:46,285) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 
(2025-08-12 20:53:46,285) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 
(2025-08-12 20:53:46,286) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 
(2025-08-12 20:53:46,286) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 
(2025-08-12 20:53:46,287) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 
(2025-08-12 20:53:46,287) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 
(2025-08-12 20:53:46,288) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 
(2025-08-12 20:53:46,288) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 
(2025-08-12 20:53:46,288) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 
(2025-08-12 20:53:46,289) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 
(2025-08-12 20:53:46,289) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 
(2025-08-12 20:53:46,290) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 
(2025-08-12 20:53:46,290) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 
(2025-08-12 20:53:46,291) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 
(2025-08-12 20:53:46,291) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 
(2025-08-12 20:53:46,292) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 
(2025-08-12 20:53:46,292) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 
(2025-08-12 20:53:46,293) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 
(2025-08-12 20:53:46,293) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 
(2025-08-12 20:53:46,294) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 
(2025-08-12 20:53:46,294) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 
(2025-08-12 20:53:46,295) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 
(2025-08-12 20:53:46,295) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 
(2025-08-12 20:53:46,296) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 
(2025-08-12 20:53:46,296) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 
(2025-08-12 20:53:46,297) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 
(2025-08-12 20:53:46,297) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 
(2025-08-12 20:53:46,298) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 
(2025-08-12 20:53:46,298) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 
(2025-08-12 20:53:46,299) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 
(2025-08-12 20:53:46,299) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 
(2025-08-12 20:53:46,299) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 
(2025-08-12 20:53:46,300) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 
(2025-08-12 20:53:46,300) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 
(2025-08-12 20:53:46,301) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 
(2025-08-12 20:53:46,302) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 
(2025-08-12 20:53:46,302) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 
(2025-08-12 20:53:46,303) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 
(2025-08-12 20:53:46,303) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 
(2025-08-12 20:53:46,304) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 
(2025-08-12 20:53:46,304) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 
(2025-08-12 20:53:46,305) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 
(2025-08-12 20:53:46,305) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 
(2025-08-12 20:53:46,306) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 
(2025-08-12 20:53:46,306) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 
(2025-08-12 20:53:46,307) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 
(2025-08-12 20:53:46,307) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 
(2025-08-12 20:53:46,308) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 
(2025-08-12 20:53:46,308) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 
(2025-08-12 20:53:46,309) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 
(2025-08-12 20:53:46,309) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 
(2025-08-12 20:53:46,310) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 
(2025-08-12 20:53:46,310) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 
(2025-08-12 20:53:46,311) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 
(2025-08-12 20:53:46,311) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 
(2025-08-12 20:53:46,312) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 
(2025-08-12 20:53:46,312) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 
(2025-08-12 20:53:46,313) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 
(2025-08-12 20:53:46,313) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 
(2025-08-12 20:53:46,314) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 
(2025-08-12 20:53:46,314) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 
(2025-08-12 20:53:46,314) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 
(2025-08-12 20:53:46,315) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 
(2025-08-12 20:53:46,315) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 
(2025-08-12 20:53:46,316) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 
(2025-08-12 20:53:46,316) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 
(2025-08-12 20:53:46,316) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 
(2025-08-12 20:53:46,317) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 
(2025-08-12 20:53:46,318) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 
(2025-08-12 20:53:46,318) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 
(2025-08-12 20:53:46,319) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 
(2025-08-12 20:53:46,320) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 
(2025-08-12 20:53:46,320) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 
(2025-08-12 20:53:46,320) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 
(2025-08-12 20:53:46,321) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 
(2025-08-12 20:53:46,321) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 
(2025-08-12 20:53:46,322) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 
(2025-08-12 20:53:46,322) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 
(2025-08-12 20:53:46,323) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 
(2025-08-12 20:53:46,323) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 
(2025-08-12 20:53:46,324) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 
(2025-08-12 20:53:46,324) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 
(2025-08-12 20:53:46,325) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 
(2025-08-12 20:53:46,325) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 
(2025-08-12 20:53:46,326) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 
(2025-08-12 20:53:46,326) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 
(2025-08-12 20:53:46,327) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 
(2025-08-12 20:53:46,327) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 
(2025-08-12 20:53:46,328) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 
(2025-08-12 20:53:46,328) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 
(2025-08-12 20:53:46,329) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 
(2025-08-12 20:53:46,329) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 
(2025-08-12 20:53:46,329) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 
(2025-08-12 20:53:46,330) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 
(2025-08-12 20:53:46,330) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 
(2025-08-12 20:53:46,331) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 
(2025-08-12 20:53:46,331) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 
(2025-08-12 20:53:46,332) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 
(2025-08-12 20:53:46,332) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 
(2025-08-12 20:53:46,332) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 
(2025-08-12 20:53:46,333) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 
(2025-08-12 20:53:46,334) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 
(2025-08-12 20:53:46,334) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 
(2025-08-12 20:53:46,334) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 
(2025-08-12 20:53:46,335) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 
(2025-08-12 20:53:46,335) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 
(2025-08-12 20:53:46,336) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 
(2025-08-12 20:53:46,708) [INFO]: Register evaluations
(2025-08-12 20:53:46,708) [INFO]: Perform evaluations. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:53:46,708) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:53:46,776) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<
(2025-08-12 20:53:46,776) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-12 20:53:53,140) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<
(2025-08-12 20:53:53,141) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-12 20:53:53,246) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:53,795) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:53,938) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,046) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,155) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,190) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,297) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,478) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,479) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,587) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,588) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,701) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:54,846) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,098) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,098) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,099) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,099) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,099) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,212) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,357) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,504) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,504) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,764) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,765) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,876) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:55,990) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,095) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,095) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,285) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,397) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,661) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,662) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,662) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,663) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,701) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,702) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:56,924) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:57,150) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:57,380) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:57,649) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:57,954) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:58,225) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:58,226) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:58,408) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:58,408) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:58,915) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:59,070) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:59,071) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:59,149) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-12 20:53:59,160) [INFO]: Plotting. Method[AnomalyDetector], Schema[naive].
(2025-08-12 20:53:59,160) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-12 20:53:59,222) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only 
(2025-08-12 20:54:07,969) [INFO]:     [AnomalyDetector] Plot dataset WSD score only 
(2025-08-13 15:51:08,652) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 15:51:08,653) [INFO]: Dataset Directory has been loaded.
(2025-08-13 15:51:08,669) [INFO]: Run Experiments. Method[YetAnotherAnomalyDetector], Schema[naive].
(2025-08-13 15:51:08,669) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 15:51:08,670) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-13 15:51:09,061) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-13 15:51:34,838) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-13 15:55:37,058) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 15:55:37,059) [INFO]: Dataset Directory has been loaded.
(2025-08-13 15:55:37,079) [INFO]: Run Experiments. Method[YetAnotherAnomalyDetector], Schema[naive].
(2025-08-13 15:55:37,079) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 15:55:37,080) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-13 15:55:37,167) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-13 15:56:00,835) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-13 15:56:24,951) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-13 15:56:46,012) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 
(2025-08-13 15:57:10,240) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 
(2025-08-13 15:57:34,144) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c 
(2025-08-13 15:57:51,713) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 
(2025-08-13 15:58:09,372) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 
(2025-08-13 15:58:30,449) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa 
(2025-08-13 15:58:54,597) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 
(2025-08-13 15:58:55,270) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 
(2025-08-13 15:59:16,810) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd 
(2025-08-13 15:59:41,019) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd 
(2025-08-13 15:59:41,686) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 
(2025-08-13 15:59:59,333) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd 
(2025-08-13 16:00:16,524) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 
(2025-08-13 16:00:40,636) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-13 16:01:04,701) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d 
(2025-08-13 16:01:05,353) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 
(2025-08-13 16:01:06,609) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da 
(2025-08-13 16:01:27,744) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea 
(2025-08-13 16:01:28,408) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa 
(2025-08-13 16:01:49,475) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 
(2025-08-13 16:01:50,115) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 
(2025-08-13 16:01:50,759) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 
(2025-08-13 16:02:15,057) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d 
(2025-08-13 16:02:39,197) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 
(2025-08-13 16:03:00,478) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af 
(2025-08-13 16:03:24,746) [INFO]:     [YetAnotherAnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 
(2025-08-13 16:03:45,999) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 135 
(2025-08-13 16:03:47,767) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 61 
(2025-08-13 16:03:49,990) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 95 
(2025-08-13 16:03:52,229) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 132 
(2025-08-13 16:03:54,454) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 59 
(2025-08-13 16:03:56,598) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 92 
(2025-08-13 16:03:58,846) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 66 
(2025-08-13 16:04:01,099) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 104 
(2025-08-13 16:04:03,336) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 50 
(2025-08-13 16:04:05,552) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 68 
(2025-08-13 16:04:07,785) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 103 
(2025-08-13 16:04:10,056) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 57 
(2025-08-13 16:04:12,247) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 168 
(2025-08-13 16:04:14,426) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 157 
(2025-08-13 16:04:16,649) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 150 
(2025-08-13 16:04:18,895) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 159 
(2025-08-13 16:04:21,162) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 32 
(2025-08-13 16:04:23,366) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 166 
(2025-08-13 16:04:25,580) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 192 
(2025-08-13 16:04:27,788) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 35 
(2025-08-13 16:04:30,019) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 195 
(2025-08-13 16:04:32,234) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 161 
(2025-08-13 16:04:34,429) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 102 
(2025-08-13 16:04:36,602) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 69 
(2025-08-13 16:04:38,813) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 56 
(2025-08-13 16:04:41,062) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 105 
(2025-08-13 16:04:43,318) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 51 
(2025-08-13 16:04:45,522) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 58 
(2025-08-13 16:04:47,864) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 133 
(2025-08-13 16:04:50,182) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 67 
(2025-08-13 16:04:52,398) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 93 
(2025-08-13 16:04:54,623) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 134 
(2025-08-13 16:04:56,777) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 94 
(2025-08-13 16:04:59,040) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 60 
(2025-08-13 16:05:01,287) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 34 
(2025-08-13 16:05:03,543) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 160 
(2025-08-13 16:05:05,774) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 194 
(2025-08-13 16:05:08,005) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 33 
(2025-08-13 16:05:10,263) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 158 
(2025-08-13 16:05:12,520) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 193 
(2025-08-13 16:05:14,762) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 167 
(2025-08-13 16:05:16,974) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 151 
(2025-08-13 16:05:19,187) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 169 
(2025-08-13 16:05:21,397) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 156 
(2025-08-13 16:05:23,601) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 204 
(2025-08-13 16:05:25,841) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 203 
(2025-08-13 16:05:28,080) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 202 
(2025-08-13 16:05:30,303) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 205 
(2025-08-13 16:05:32,525) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 174 
(2025-08-13 16:05:34,743) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 180 
(2025-08-13 16:05:36,964) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 20 
(2025-08-13 16:05:39,211) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 187 
(2025-08-13 16:05:41,444) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 18 
(2025-08-13 16:05:43,655) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 173 
(2025-08-13 16:05:45,855) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 27 
(2025-08-13 16:05:48,063) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 9 
(2025-08-13 16:05:50,273) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 0 
(2025-08-13 16:05:52,493) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 145 
(2025-08-13 16:05:54,820) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 11 
(2025-08-13 16:05:57,013) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 142 
(2025-08-13 16:05:59,232) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 7 
(2025-08-13 16:06:01,451) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 29 
(2025-08-13 16:06:03,660) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 16 
(2025-08-13 16:06:05,864) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 189 
(2025-08-13 16:06:08,091) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 129 
(2025-08-13 16:06:10,309) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 42 
(2025-08-13 16:06:12,549) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 89 
(2025-08-13 16:06:14,766) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 116 
(2025-08-13 16:06:16,997) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 45 
(2025-08-13 16:06:19,177) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 111 
(2025-08-13 16:06:21,426) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 73 
(2025-08-13 16:06:23,669) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 118 
(2025-08-13 16:06:25,879) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 87 
(2025-08-13 16:06:28,079) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 127 
(2025-08-13 16:06:30,288) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 80 
(2025-08-13 16:06:32,524) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 74 
(2025-08-13 16:06:34,758) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 120 
(2025-08-13 16:06:36,960) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 6 
(2025-08-13 16:06:39,168) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 28 
(2025-08-13 16:06:41,374) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 143 
(2025-08-13 16:06:43,586) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 17 
(2025-08-13 16:06:45,780) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 188 
(2025-08-13 16:06:47,991) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 144 
(2025-08-13 16:06:50,142) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 1 
(2025-08-13 16:06:52,350) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 10 
(2025-08-13 16:06:54,694) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 172 
(2025-08-13 16:06:56,942) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 186 
(2025-08-13 16:06:59,184) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 19 
(2025-08-13 16:07:01,418) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 26 
(2025-08-13 16:07:03,633) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 8 
(2025-08-13 16:07:05,850) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 181 
(2025-08-13 16:07:08,075) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 175 
(2025-08-13 16:07:10,318) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 21 
(2025-08-13 16:07:12,530) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 75 
(2025-08-13 16:07:14,709) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 81 
(2025-08-13 16:07:16,961) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 121 
(2025-08-13 16:07:19,175) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 119 
(2025-08-13 16:07:21,389) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 86 
(2025-08-13 16:07:23,606) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 72 
(2025-08-13 16:07:25,815) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 126 
(2025-08-13 16:07:28,040) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 44 
(2025-08-13 16:07:30,271) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 110 
(2025-08-13 16:07:32,515) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 43 
(2025-08-13 16:07:34,715) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 128 
(2025-08-13 16:07:36,909) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 88 
(2025-08-13 16:07:39,130) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 117 
(2025-08-13 16:07:41,362) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 198 
(2025-08-13 16:07:43,588) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 153 
(2025-08-13 16:07:45,820) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 38 
(2025-08-13 16:07:48,021) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 154 
(2025-08-13 16:07:50,232) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 36 
(2025-08-13 16:07:52,459) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 196 
(2025-08-13 16:07:54,646) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 162 
(2025-08-13 16:07:56,860) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 31 
(2025-08-13 16:07:59,077) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 165 
(2025-08-13 16:08:01,277) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 191 
(2025-08-13 16:08:03,495) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 131 
(2025-08-13 16:08:05,706) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 91 
(2025-08-13 16:08:07,917) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 65 
(2025-08-13 16:08:10,139) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 136 
(2025-08-13 16:08:12,897) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 62 
(2025-08-13 16:08:15,097) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 96 
(2025-08-13 16:08:17,296) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 109 
(2025-08-13 16:08:19,496) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 100 
(2025-08-13 16:08:21,717) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 54 
(2025-08-13 16:08:23,927) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 107 
(2025-08-13 16:08:26,132) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 98 
(2025-08-13 16:08:28,323) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 138 
(2025-08-13 16:08:30,538) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 53 
(2025-08-13 16:08:32,684) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 30 
(2025-08-13 16:08:34,892) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 190 
(2025-08-13 16:08:37,102) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 164 
(2025-08-13 16:08:39,307) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 37 
(2025-08-13 16:08:41,495) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 163 
(2025-08-13 16:08:43,687) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 197 
(2025-08-13 16:08:45,892) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 155 
(2025-08-13 16:08:48,071) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 199 
(2025-08-13 16:08:50,280) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 39 
(2025-08-13 16:08:52,479) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 152 
(2025-08-13 16:08:54,709) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 106 
(2025-08-13 16:08:56,906) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 99 
(2025-08-13 16:08:59,123) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 52 
(2025-08-13 16:09:01,444) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 139 
(2025-08-13 16:09:03,647) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 101 
(2025-08-13 16:09:05,858) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 55 
(2025-08-13 16:09:08,071) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 137 
(2025-08-13 16:09:10,172) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 97 
(2025-08-13 16:09:12,382) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 108 
(2025-08-13 16:09:14,549) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 63 
(2025-08-13 16:09:16,735) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 130 
(2025-08-13 16:09:18,942) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 64 
(2025-08-13 16:09:21,135) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 90 
(2025-08-13 16:09:23,329) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 209 
(2025-08-13 16:09:25,526) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 200 
(2025-08-13 16:09:27,718) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 207 
(2025-08-13 16:09:29,935) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 206 
(2025-08-13 16:09:32,136) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 201 
(2025-08-13 16:09:34,340) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 208 
(2025-08-13 16:09:36,552) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 46 
(2025-08-13 16:09:38,860) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 79 
(2025-08-13 16:09:41,078) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 112 
(2025-08-13 16:09:43,289) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 41 
(2025-08-13 16:09:45,498) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 115 
(2025-08-13 16:09:47,718) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 83 
(2025-08-13 16:09:49,920) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 77 
(2025-08-13 16:09:52,159) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 123 
(2025-08-13 16:09:54,347) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 48 
(2025-08-13 16:09:56,552) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 70 
(2025-08-13 16:09:58,750) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 84 
(2025-08-13 16:10:00,954) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 124 
(2025-08-13 16:10:03,172) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 184 
(2025-08-13 16:10:05,358) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 170 
(2025-08-13 16:10:07,580) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 24 
(2025-08-13 16:10:09,793) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 177 
(2025-08-13 16:10:11,988) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 183 
(2025-08-13 16:10:14,205) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 148 
(2025-08-13 16:10:16,873) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 23 
(2025-08-13 16:10:19,078) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 141 
(2025-08-13 16:10:21,280) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 4 
(2025-08-13 16:10:23,490) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 15 
(2025-08-13 16:10:25,698) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 3 
(2025-08-13 16:10:27,900) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 146 
(2025-08-13 16:10:30,024) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 12 
(2025-08-13 16:10:32,204) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 179 
(2025-08-13 16:10:34,399) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 85 
(2025-08-13 16:10:36,619) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 71 
(2025-08-13 16:10:38,827) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 125 
(2025-08-13 16:10:41,035) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 76 
(2025-08-13 16:10:43,229) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 82 
(2025-08-13 16:10:45,434) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 49 
(2025-08-13 16:10:47,640) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 122 
(2025-08-13 16:10:49,841) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 40 
(2025-08-13 16:10:52,058) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 114 
(2025-08-13 16:10:54,278) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 47 
(2025-08-13 16:10:56,409) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 113 
(2025-08-13 16:10:58,625) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 78 
(2025-08-13 16:11:00,827) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 147 
(2025-08-13 16:11:02,581) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 2 
(2025-08-13 16:11:04,826) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 178 
(2025-08-13 16:11:07,035) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 13 
(2025-08-13 16:11:09,248) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 5 
(2025-08-13 16:11:11,454) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 140 
(2025-08-13 16:11:13,657) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 14 
(2025-08-13 16:11:15,879) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 182 
(2025-08-13 16:11:18,092) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 176 
(2025-08-13 16:11:20,297) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 22 
(2025-08-13 16:11:22,491) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 149 
(2025-08-13 16:11:24,703) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 171 
(2025-08-13 16:11:26,938) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 185 
(2025-08-13 16:11:29,130) [INFO]:     [YetAnotherAnomalyDetector] handling dataset WSD | curve 25 
(2025-08-13 16:11:31,714) [INFO]: Register evaluations
(2025-08-13 16:11:31,714) [INFO]: Perform evaluations. Method[YetAnotherAnomalyDetector], Schema[naive].
(2025-08-13 16:11:31,714) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-13 16:11:32,101) [INFO]:     [YetAnotherAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-13 16:11:32,102) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-13 16:11:38,583) [INFO]:     [YetAnotherAnomalyDetector] Eval dataset WSD <<<
(2025-08-13 16:11:38,583) [INFO]:         [WSD] Using margins (0, 3)
(2025-08-13 16:11:38,690) [WARNING]: [YetAnotherAnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,251) [WARNING]: [YetAnotherAnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,396) [WARNING]: [YetAnotherAnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,507) [WARNING]: [YetAnotherAnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,621) [WARNING]: [YetAnotherAnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,656) [WARNING]: [YetAnotherAnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,767) [WARNING]: [YetAnotherAnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,951) [WARNING]: [YetAnotherAnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:39,951) [WARNING]: [YetAnotherAnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,061) [WARNING]: [YetAnotherAnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,062) [WARNING]: [YetAnotherAnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,175) [WARNING]: [YetAnotherAnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,324) [WARNING]: [YetAnotherAnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,583) [WARNING]: [YetAnotherAnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,584) [WARNING]: [YetAnotherAnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,585) [WARNING]: [YetAnotherAnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,586) [WARNING]: [YetAnotherAnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,586) [WARNING]: [YetAnotherAnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,702) [WARNING]: [YetAnotherAnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,851) [WARNING]: [YetAnotherAnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:40,999) [WARNING]: [YetAnotherAnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,000) [WARNING]: [YetAnotherAnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,268) [WARNING]: [YetAnotherAnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,269) [WARNING]: [YetAnotherAnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,380) [WARNING]: [YetAnotherAnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,497) [WARNING]: [YetAnotherAnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,604) [WARNING]: [YetAnotherAnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,605) [WARNING]: [YetAnotherAnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,794) [WARNING]: [YetAnotherAnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:41,905) [WARNING]: [YetAnotherAnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,175) [WARNING]: [YetAnotherAnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,176) [WARNING]: [YetAnotherAnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,177) [WARNING]: [YetAnotherAnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,178) [WARNING]: [YetAnotherAnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,218) [WARNING]: [YetAnotherAnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,219) [WARNING]: [YetAnotherAnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,451) [WARNING]: [YetAnotherAnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,673) [WARNING]: [YetAnotherAnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:42,909) [WARNING]: [YetAnotherAnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:43,178) [WARNING]: [YetAnotherAnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:43,490) [WARNING]: [YetAnotherAnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:43,766) [WARNING]: [YetAnotherAnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:43,767) [WARNING]: [YetAnotherAnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:43,955) [WARNING]: [YetAnotherAnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:43,956) [WARNING]: [YetAnotherAnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:44,458) [WARNING]: [YetAnotherAnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:44,615) [WARNING]: [YetAnotherAnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:44,616) [WARNING]: [YetAnotherAnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:44,695) [WARNING]: [YetAnotherAnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<
(2025-08-13 16:11:44,709) [INFO]: Plotting. Method[YetAnotherAnomalyDetector], Schema[naive].
(2025-08-13 16:11:44,709) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD 
(2025-08-13 16:11:44,772) [INFO]:     [YetAnotherAnomalyDetector] Plot dataset AIOPS score only 
(2025-08-13 16:11:53,847) [INFO]:     [YetAnotherAnomalyDetector] Plot dataset WSD score only 
(2025-08-13 17:30:15,478) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:30:15,480) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:30:15,480) [WARNING]: Finding Multiple datasets. All curves in these datasets will be employed, and the param "curve_names" will be ignored.
(2025-08-13 17:30:31,769) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:30:31,769) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:30:31,771) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:30:31,771) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:30:42,627) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:30:42,629) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:32:35,821) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:32:35,822) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:32:41,821) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:32:41,822) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 17:32:41,824) [INFO]:     [Load Data (All)] DataSets: AIOPS 
(2025-08-13 17:32:41,893) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 
(2025-08-13 17:33:05,153) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 
(2025-08-13 17:33:28,891) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 
(2025-08-13 17:34:31,291) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:34:31,293) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:34:38,094) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:34:38,095) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 17:34:38,096) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:34:38,100) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-13 17:35:02,237) [INFO]: Register evaluations
(2025-08-13 17:35:02,238) [INFO]: Perform evaluations. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:35:02,238) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:35:02,239) [INFO]:     [GeneratedAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-13 17:35:02,240) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-13 17:37:20,413) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:37:20,413) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:37:20,415) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:37:20,415) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:37:44,750) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:37:44,750) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:37:44,751) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 17:37:44,751) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 17:37:44,753) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:37:44,753) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:37:44,758) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-13 17:37:44,758) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-13 17:37:59,282) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:37:59,284) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:38:06,317) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:38:06,318) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 17:38:06,319) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:38:06,320) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-13 17:38:30,681) [INFO]: Register evaluations
(2025-08-13 17:38:30,681) [INFO]: Perform evaluations. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:38:30,682) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:38:30,684) [INFO]:     [GeneratedAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-13 17:38:30,685) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-13 17:39:41,953) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-13 17:39:41,954) [INFO]: Dataset Directory has been loaded.
(2025-08-13 17:39:41,965) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:39:41,966) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-13 17:39:41,966) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:39:41,970) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-13 17:40:05,793) [INFO]: Register evaluations
(2025-08-13 17:40:05,794) [INFO]: Perform evaluations. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:40:05,794) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:40:05,795) [INFO]:     [GeneratedAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-13 17:40:05,796) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-13 17:40:06,136) [INFO]: Plotting. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-13 17:40:06,137) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-13 17:40:06,138) [INFO]:     [GeneratedAnomalyDetector] Plot dataset AIOPS score only 
(2025-08-14 14:07:41,526) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 14:07:41,528) [INFO]: Dataset Directory has been loaded.
(2025-08-14 14:08:01,181) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:08:01,182) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 14:08:01,184) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:08:01,188) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 14:14:28,821) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 14:14:28,822) [INFO]: Dataset Directory has been loaded.
(2025-08-14 14:14:28,840) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:14:28,840) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 14:14:28,841) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:14:28,844) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 14:14:44,509) [INFO]: Register evaluations
(2025-08-14 14:14:44,509) [INFO]: Perform evaluations. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:14:44,509) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:14:44,514) [INFO]:     [GeneratedAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-14 14:14:44,515) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-14 14:14:44,618) [INFO]: Plotting. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:14:44,618) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:14:44,620) [INFO]:     [GeneratedAnomalyDetector] Plot dataset AIOPS score only 
(2025-08-14 14:37:58,822) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 14:37:58,823) [INFO]: Dataset Directory has been loaded.
(2025-08-14 14:37:58,843) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:37:58,844) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 14:37:58,845) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:37:58,848) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 14:45:03,992) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 14:45:03,993) [INFO]: Dataset Directory has been loaded.
(2025-08-14 14:45:04,012) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:45:04,012) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 14:45:04,013) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:45:04,016) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 14:45:23,431) [INFO]: Register evaluations
(2025-08-14 14:45:23,432) [INFO]: Perform evaluations. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:45:23,432) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:45:23,433) [INFO]:     [GeneratedAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-14 14:45:23,433) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-14 14:45:23,527) [INFO]: Plotting. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 14:45:23,527) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 14:45:23,528) [INFO]:     [GeneratedAnomalyDetector] Plot dataset AIOPS score only 
(2025-08-14 15:10:52,244) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:14:53,111) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:22:24,703) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 15:22:24,704) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 15:22:24,705) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:22:24,709) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 15:23:32,052) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 15:23:32,052) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 15:23:32,053) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:23:32,055) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 15:23:39,261) [INFO]: Run Experiments. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 15:23:39,262) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml
(2025-08-14 15:23:39,263) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:23:39,265) [INFO]:     [GeneratedAnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 
(2025-08-14 15:23:58,644) [INFO]: Register evaluations
(2025-08-14 15:23:58,645) [INFO]: Perform evaluations. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 15:23:58,645) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:23:58,649) [INFO]:     [GeneratedAnomalyDetector] Eval dataset AIOPS <<<
(2025-08-14 15:23:58,649) [INFO]:         [AIOPS] Using margins (0, 5)
(2025-08-14 15:23:58,743) [INFO]: Plotting. Method[GeneratedAnomalyDetector], Schema[naive].
(2025-08-14 15:23:58,744) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:23:58,745) [INFO]:     [GeneratedAnomalyDetector] Plot dataset AIOPS score only 
(2025-08-14 15:23:59,200) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:53:24,728) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 15:58:22,859) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 15:58:22,861) [INFO]: Dataset Directory has been loaded.
(2025-08-14 15:58:46,252) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:14:10,158) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 16:14:10,160) [INFO]: Dataset Directory has been loaded.
(2025-08-14 16:14:47,254) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:24:32,517) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:28:38,817) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:31:53,289) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:34:26,879) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:39:36,022) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:40:38,159) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:42:04,996) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:50:38,896) [INFO]: 
                         
███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ 
██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗
█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║
██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║
███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝
╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ 
                                                                      
                         
(2025-08-14 16:50:38,898) [INFO]: Dataset Directory has been loaded.
(2025-08-14 16:51:42,956) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:52:11,502) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:54:57,126) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:58:01,780) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 16:59:08,312) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:02:15,095) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:08:05,641) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:09:32,855) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:10:19,071) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:12:57,276) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:22:25,678) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:24:48,255) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:31:41,157) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:33:45,134) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:34:33,838) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
(2025-08-14 17:35:07,418) [INFO]:     [Load Data (Specify)] DataSets: AIOPS 
